package com.uino.api.client.permission;

import java.util.List;
import java.util.Set;

import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.business.OrgNodeInfo;
import com.uino.bean.permission.business.request.AddOrRemoveRoleToOrgRequestDto;
import com.uino.bean.permission.business.request.AddOrRemoveUserToOrgRequestDto;
import com.uino.bean.permission.business.request.InterchangeOrgNoRequestDto;
import com.uino.bean.permission.business.request.SaveOrgRequestDto;
import com.uino.bean.permission.business.request.SetUsersOrgsRequestDto;
import com.uino.bean.permission.query.CSysOrg;

/**
 * 组织服务
 * 
 * <AUTHOR>
 */
public interface IOrgApiSvc {

    /**
     * 获取组织tree
     * 
     * @return
     */
    OrgNodeInfo getOrgTree();

    /**
     * 根据传入的rootOrg成树
     * @param domainId 领域
     * @param rootOrg 组织
     * @param findUser 是否查询组织下的用户
     * @return
     */
    public OrgNodeInfo getOrgTree(Long domainId, Long rootOrg, boolean findUser);

    OrgNodeInfo getOrgTree(Long domainId);

    SysOrg getRootOrg(Long domainId);

    /**
     * 根据父级id查询组织列表
     *
     * @param parentId
     * @return
     */
    List<SysOrg> getOrgListByParentId(Long parentId);


    /**
	 * 根据数据量动态获取组织tree，组织数<=阈值，返回全量数据，组织数>阈值，返回一级子组织
	 * @param orgId 父级组织id(为空默认从根组织开始)
	 */
	public OrgNodeInfo getOrgTreeV2(Long orgId);
    public OrgNodeInfo getOrgTreeV2(Long domainId,Long orgId);

    OrgNodeInfo getOrgTreeV3(Long domainId);

    List<OrgNodeInfo> getAllOrgTree(Set<Long> domainIds);

    /**
     * 持久化组织
     * 
     * @param request
     * @return
     */
    Long saveOrUpdateOrg(SaveOrgRequestDto request);

    /**
     * 移除组织
     * 
     * @param removeOrgIds
     */
    void deleteOrg(Set<Long> removeOrgIds);

    /**
     * 向某个组织下添加用户
     * 
     * @param request
     */
    void addUserForOrg(AddOrRemoveUserToOrgRequestDto request);

    /**
     * 将用户从某组织下移除
     * 
     * @param request
     */
    void removeUserForOrg(AddOrRemoveUserToOrgRequestDto request);

    /**
     * 设置某些用户的所在组织
     * 
     * @param reqDto
     */
    void setUsersOrgs(SetUsersOrgsRequestDto reqDto);

    /**
     * 向某个组织绑定角色
     * 
     * @param request
     */
    void addRoleForOrg(AddOrRemoveRoleToOrgRequestDto request);

    /**
     * 将某些角色从组织下移除
     * 
     * @param request
     */
    void removeRoleForOrg(AddOrRemoveRoleToOrgRequestDto request);

    /**
     * 获取指定组织下的用户
     * 
     * @param orgId
     * @return
     */
    Set<Long> getUserIds(Long orgId);

    /**
     * 获取指定组织下的角色
     * 
     * @param orgId
     * @return
     */
    Set<Long> getRoleIds(Long orgId);

    /**
     * 获取指定角色下的组织
     * 
     * @param roleId
     * @return
     */
    List<SysOrg> getOrgByRoleId(Long roleId);

    /**
     * 根据查询条件分页查询组织
     * 
     * @param pageNum
     * @param pageSize
     * @param query
     * @return
     */
    Page<SysOrg> queryPageByCdt(int pageNum, int pageSize, CSysOrg query);

    /**
     * 不分页条件查询组织
     * 
     * @param query
     * @return
     */
    List<SysOrg> queryListByCdt(CSysOrg query);

    /**
     * 交换组织顺序
     * 
     * @param reqDto
     */
    void interchangeOrgNo(InterchangeOrgNoRequestDto reqDto);

    /**
     *  获取当前用户可见的组织树
     * @return
     */
    List<OrgNodeInfo> getTreeByCurrentUser();
}
