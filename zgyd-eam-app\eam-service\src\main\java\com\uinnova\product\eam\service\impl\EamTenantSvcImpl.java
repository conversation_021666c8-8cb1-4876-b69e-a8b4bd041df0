package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.core.io.Compression;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.EamTenantVo;
import com.uinnova.product.eam.comm.model.es.AppSquareConfig;
import com.uinnova.product.eam.comm.model.es.EamTenant;
import com.uinnova.product.eam.model.saas.TenantSaveReq;
import com.uinnova.product.eam.service.EamTenantSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.es.AppSquareConfigDao;
import com.uinnova.product.eam.service.es.EamTenantDao;
import com.uinnova.product.eam.service.utils.PinYinUtil;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.permission.IRoleApiSvc;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.permission.base.*;
import com.uino.bean.permission.business.DataRole;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.OrgNodeInfo;
import com.uino.bean.permission.business.request.BusinessConfigDto;
import com.uino.bean.permission.query.CSysUserOrgRlt;
import com.uino.bean.permission.query.CSysUserRoleRlt;
import com.uino.bean.sys.base.ESDictionaryAttrDef;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.enums.DictionaryOptionEnum;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.dao.permission.ESDataModuleSvc;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.dao.permission.ESOrgSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.permission.rlt.ESUserOrgRltSvc;
import com.uino.dao.permission.rlt.ESUserRoleRltSvc;
import com.uino.dao.saas.constant.DomainReqType;
import com.uino.dao.saas.constant.SaaSConstant;
import com.uino.dao.saas.context.DomainContext;
import com.uino.dao.saas.context.DomainContextValue;
import com.uino.dao.sys.ESDictionaryClassSvc;
import com.uino.dao.sys.ESDictionaryItemSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.IDirSvc;
import com.uino.service.cmdb.microservice.ITaskLockSvc;
import com.uino.service.permission.microservice.IOrgSvc;
import com.uino.service.permission.microservice.impl.RoleSvc;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.digest.impl.type.Base64Util;
import com.uino.util.sys.CommonFileUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EamTenantSvcImpl implements EamTenantSvc {

    private static final long saveTenantDomainLockTime = 5 * 60 * 1000;

    private static final String lockName = "saveTenantDomain";

    private static final String ADMIN_ROLE_NAME = "admin";

    @Autowired
    private ITaskLockSvc taskLockSvc;

    @Autowired
    private EamTenantDao tenantDao;

    @Autowired
    private AppSquareConfigDao esAppSquareConfigDao;

    @Autowired
    private ESDictionaryClassSvc esDictionaryClassSvc;

    @Autowired
    private ESDictionaryItemSvc esDictionaryItemSvc;

    @Autowired
    private ESUserRoleRltSvc esUserRoleRltSvc;

    @Autowired
    private ESUserRoleRltSvc userRoleRltSvc;

    @Autowired
    private ESUserOrgRltSvc esUserOrgRltSvc;

    @Autowired
    private ESDataModuleSvc esDataModuleSvc;

    @Autowired
    private IResourceSvc iResourceSvc;

    @Autowired
    private ESModuleSvc esModuleSvc;

    @Autowired
    private BmConfigSvc bmConfigSvc;

    @Autowired
    private IRoleApiSvc roleApiSvc;

    @Autowired
    private ESImageSvc esImageSvc;

    @Resource
    private ICIClassSvc classSvc;

    @Autowired
    private ESUserSvc userSvc;

    @Autowired
    private ESOrgSvc esOrgSvc;

    @Autowired
    private ESDirSvc esDirSvc;

    @Autowired
    private RoleSvc roleSvc;

    @Autowired
    private IOrgSvc iOrgSvc;
    @Resource
    private IDirSvc dirSvc;

    @Value("${local.resource.space}")
    private String localResourceUrl;

    @Value("${http.resource.space}")
    private String httpResourceUrl;

    @Value("${permission.http.prefix:}")
    private String commUrl;

    @Override
    public void saveOrUpdate(TenantSaveReq req) {
        Assert.notNull(req.getName(), "租户名称不能为空");
        Assert.notNull(req.getTenantId(), "租户ID不能为空");
        boolean isAdd = req.getId() == null;
        EamTenant tenant = new EamTenant();
        tenant.setTenantId(req.getTenantId());
        tenant.setName(req.getName());

        BoolQueryBuilder nameQueryBuilder = new BoolQueryBuilder();
        nameQueryBuilder.must(QueryBuilders.termQuery("name.keyword", req.getName()));
        nameQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        List<EamTenant> listByCdt = tenantDao.getListByQuery(nameQueryBuilder);
        if (!listByCdt.isEmpty() && isAdd) {
            throw new BinaryException("租户名称已存在");
        }
        BoolQueryBuilder tenantQueryBuilder = new BoolQueryBuilder();
        tenantQueryBuilder.must(QueryBuilders.termQuery("tenantId.keyword", req.getTenantId()));
        tenantQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        listByCdt = tenantDao.getListByQuery(tenantQueryBuilder);
        if (!listByCdt.isEmpty() && isAdd) {
            throw new BinaryException("租户ID已存在");
        }
        DomainContextValue domainContextValue = DomainContext.getDomainContextValue();
        DomainReqType domainReqType = domainContextValue.getDomainReqType();
        domainContextValue.setDomainReqType(DomainReqType.MANAGE);
        if (isAdd) {
            boolean saveTenantDomainLock = taskLockSvc.getLock(lockName, saveTenantDomainLockTime);
            //获取锁失败
            if (!saveTenantDomainLock) {
                throw new MessageException("正在创建域,请稍后重试！");
            }
            long domainId = ESUtil.getUUID();
            tenant.setId(domainId);
            tenant.setDataStatus(1);
            tenantDao.saveOrUpdate(tenant);
            try {
                log.info("开始初始化域数据");
                initDomainData(domainId, tenant.getName(), req.getManagers());
                log.info("结束初始化域数据");
            } catch (Exception e) {
                log.error("初始化域数据异常", e);
                rollbackDomainData(domainId);
                throw new MessageException("初始化域数据异常");
            } finally {
                domainContextValue.setDomainReqType(domainReqType);
                taskLockSvc.breakLock(lockName);
            }
        } else {
            tenant.setId(req.getId());
            if (!CollectionUtils.isEmpty(req.getManagers())) {
                updateDomainData(req.getId(), req.getManagers(), tenant.getName());
            }
        }
        tenantDao.saveOrUpdate(tenant);
    }

    @Override
    public List<EamTenant> queryList(TenantSaveReq req) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if(req != null){
            if(req.getName() != null){
                boolQueryBuilder.must(QueryBuilders.wildcardQuery("name.keyword", "*" + req.getName() + "*"));
            }
            if(req.getTenantId() != null){
                boolQueryBuilder.must(QueryBuilders.termQuery("tenantId", req.getTenantId()));
            }
        }
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        return tenantDao.getListByQuery(boolQueryBuilder);
    }

    @Override
    public List<EamTenantVo> queryListInfo(TenantSaveReq req) {
        DomainContextValue domainContextValue = DomainContext.getDomainContextValue();
        DomainReqType domainReqType = domainContextValue.getDomainReqType();
        domainContextValue.setDomainReqType(DomainReqType.MANAGE);
        List<EamTenant> list = queryList(req);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Map<Long, EamTenant> tenantMap = list.stream().collect(Collectors.toMap(EamTenant::getId, e -> e));
        List<Long> ids = list.stream().map(EamTenant::getId).toList();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termsQuery("domainId", ids));
        List<SysRole> rolesByQuery = roleSvc.getRolesByQuery(boolQueryBuilder);
        Map<Long, List<SysUser>> domainUserMap = new HashMap<>();
        if (!rolesByQuery.isEmpty()) {
            List<Long> roleIds = rolesByQuery.stream().filter(role -> role.getRoleName().equals(ADMIN_ROLE_NAME)).map(SysRole::getId).toList();
            boolQueryBuilder.must(QueryBuilders.termsQuery("roleId", roleIds));
            List<SysUserRoleRlt> listByQuery = esUserRoleRltSvc.getListByQuery(boolQueryBuilder);
            if (!listByQuery.isEmpty()) {
                List<Long> userIds = listByQuery.stream().map(SysUserRoleRlt::getUserId).toList();
                List<SysUser> userList = userSvc.getListByQuery(QueryBuilders.termsQuery("id", userIds));
                Map<Long, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getId, Function.identity()));
                Map<Long, List<Long>> collect = listByQuery.stream()
                        .collect(Collectors.groupingBy(
                                SysUserRoleRlt::getDomainId,
                                Collectors.mapping(SysUserRoleRlt::getUserId, Collectors.toList())
                        ));
                for (Map.Entry<Long, List<Long>> entry : collect.entrySet()) {
                    Long domainId = entry.getKey();
                    List<Long> userIdList = entry.getValue();
                    for (Long userId : userIdList) {
                        SysUser user = userMap.get(userId);
                        if (user != null) {
                            SysUser sysUser = new SysUser();
                            sysUser.setId(userId);
                            sysUser.setUserName(user.getUserName());
                            sysUser.setLoginCode(user.getLoginCode());
                            sysUser.setDomainId(domainId);
                            if (domainUserMap.containsKey(domainId)) {
                                domainUserMap.get(domainId).add(sysUser);
                            } else {
                                List<SysUser> users = new ArrayList<>();
                                users.add(sysUser);
                                domainUserMap.put(domainId, users);
                            }
                        }
                    }
                }
            }
        }
        List<EamTenantVo> resList = new ArrayList<>();
        tenantMap.forEach((id, tenant) -> {
            EamTenantVo eamTenantVo = new EamTenantVo();
            eamTenantVo.setEamTenant(tenant);
            eamTenantVo.setSysUser(domainUserMap.get(id));
            resList.add(eamTenantVo);
        });
        domainContextValue.setDomainReqType(domainReqType);
        return resList;
    }

    @Override
    public List<OrgNodeInfo> getAllOrgTree() {
        List<EamTenant> tenants = queryList(null);
        if (CollectionUtils.isEmpty(tenants)) {
            return Collections.emptyList();
        }
        Set<Long> domainIds = tenants.stream().map(EamTenant::getId).collect(Collectors.toSet());
        return iOrgSvc.getAllOrgTree(domainIds);
    }


    @Override
    public void deleteById(Long tenantId) {
        Assert.notNull(tenantId, "租户ID不能为空");
        //查询所属租户下是否存在信息
        List<EamTenant> tenants = tenantDao.getListByQuery(QueryBuilders.termQuery("id", tenantId));
        if (CollectionUtils.isEmpty(tenants)) {
            throw new MessageException("该租户不存在");
        }
        EamTenant eamTenant = tenants.get(0);
        eamTenant.setDataStatus(0);
        tenantDao.saveOrUpdate(eamTenant);
    }


    @Override
    public void deleteTenantRoleRlt(TenantSaveReq req) {
        Assert.notNull(req.getTenantId(), "租户ID不能为空");
        if (CollectionUtils.isEmpty(req.getManagers())) {
            return;
        }
        DomainContextValue domainContextValue = DomainContext.getDomainContextValue();
        DomainReqType domainReqType = domainContextValue.getDomainReqType();
        domainContextValue.setDomainReqType(DomainReqType.MANAGE);
        Set<String> managersIds = req.getManagers();
        List<Long> userIds = managersIds.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<SysUser> users = userSvc.getByIds(userIds);
        if (CollectionUtils.isEmpty(users)) {
            log.error("用户不存在");
            return;
        }
        for (SysUser user : users) {
            if ("admin".equals(user.getLoginCode())) {
                log.error("admin用户权限不能删除");
                return;
            }
        }
        BoolQueryBuilder roleQuery = new BoolQueryBuilder();
        roleQuery.must(QueryBuilders.termQuery("domainId", req.getTenantId()));
        roleQuery.must(QueryBuilders.termsQuery("roleName", ADMIN_ROLE_NAME));
        List<SysRole> rolesByQuery = roleSvc.getRolesByQuery(roleQuery);
        if (CollectionUtils.isEmpty(rolesByQuery)) {
            throw new MessageException("缺少admin角色");
        }
        SysRole sysRole = rolesByQuery.get(0);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("domainId", req.getTenantId()));
        boolQueryBuilder.must(QueryBuilders.termQuery("roleId", sysRole.getId()));
        boolQueryBuilder.must(QueryBuilders.termsQuery("userId", req.getManagers()));
        esUserRoleRltSvc.deleteByQuery(boolQueryBuilder, true);
        domainContextValue.setDomainReqType(domainReqType);
    }


    private void initDomainData(Long domainId, String tenantName, Set<String> managers) {
        initResource(domainId);
        Map<Long, Long> dirMap = initUinoCiDir(domainId);
        initUinoCmdbImage(domainId, dirMap);
        initUinoSysDataModule(domainId);
        initUinoSysModule(domainId);
        Map<Long, Long> dictionaryClass = initUinoSysDictionaryClass(domainId);
        initUinoSysDictionaryItem(domainId, dictionaryClass);
        initAppSquareConfigData(domainId);
        initSystemConfigData(domainId);
        Map<String, Long> stringLongMap = initRoleData(domainId);
        Long managerRoleId = 0L;
        if (!CollectionUtils.isEmpty(stringLongMap) && stringLongMap.containsKey(ADMIN_ROLE_NAME)) {
            managerRoleId = stringLongMap.get(ADMIN_ROLE_NAME);
            //处理角色权限
            dealRolePermission(managerRoleId, domainId);
        }
        Map<Long, Long> orgData = initOrgData(domainId, tenantName);
        if (CollectionUtils.isEmpty(managers)) {
            managers = new HashSet<>();
        }
        //默认添加admin
        Set<String> newManagerIds = initDefaultManagerUser(domainId, tenantName);
        managers.addAll(newManagerIds);
        Set<String> managersUserIds = createUser(domainId, managers);
        dealManagerRoleRlt(managersUserIds, managerRoleId, domainId);
        dealManagerOrgRlt(managersUserIds, orgData);
    }


    private void updateDomainData(Long domainId, Set<String> managers, String tenantName) {
        //过滤新用户
        Set<String> newUserIds = createUser(domainId, managers);
        //处理用户与角色
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termQuery("roleName.keyword", ADMIN_ROLE_NAME));
        List<SysRole> rolesByQuery = roleSvc.getRolesByQuery(query);
        if (rolesByQuery.isEmpty()) {
            throw new BinaryException("缺少admin角色");
        }
        Long managerRoleId = rolesByQuery.get(0).getId();
        //过滤没有建立关系的用户
        filterManagers(newUserIds, managerRoleId, domainId);
        dealManagerRoleRlt(newUserIds, managerRoleId, domainId);
        //处理用户与组织
        BoolQueryBuilder orgQuery = QueryBuilders.boolQuery();
        orgQuery.must(QueryBuilders.termQuery("domainId", domainId));
        orgQuery.must(QueryBuilders.termQuery("parentOrgId", 0L));
        List<SysOrg> orgListByParentId = esOrgSvc.getListByQuery(orgQuery);
        //过滤没有建立组织的用户
        filterManagersForNoOrg(newUserIds, orgListByParentId, domainId);
        Map<Long, Long> orgIdMap = initOrgData(domainId, tenantName);
        dealManagerOrgRlt(newUserIds, orgIdMap);
    }

    private Set<String> createUser(Long domainId, Set<String> managers) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termsQuery("id", managers.toArray(new String[0])));
        List<SysUser> listByQuery = userSvc.getListByQuery(boolQueryBuilder);
        Set<String> managerIds = new HashSet<>();
        if (!listByQuery.isEmpty()) {
            List<SysUser> newUsers = new ArrayList<>();
            listByQuery.forEach(user -> {
                if (!Objects.equals(user.getDomainId(), domainId)) {
                    newUsers.add(user);
                } else {
                    managerIds.add(user.getId().toString());
                }
            });
            Set<String> userLoginCodeCache = new HashSet<>();
            for (SysUser user : newUsers) {
                if (userLoginCodeCache.contains(user.getLoginCode())) {
                    continue;
                }
                userLoginCodeCache.add(user.getLoginCode());
                long uuid = ESUtil.getUUID();
                managerIds.add(String.valueOf(uuid));
                user.setId(uuid);
            }
            userSvc.saveOrUpdateBatch(listByQuery);
        }
        return managerIds;
    }

    private Set<String> initDefaultManagerUser(Long domainId, String tenantName){
        Set<String> managerIds = new HashSet<>();
        List<SysUser> datas = CommonFileUtil.getData("/initdata/uino_sys_default_user.json",
                SysUser.class);
        if(!CollectionUtils.isEmpty(datas)){
            List<SysUser> userList = new ArrayList<>();
            for (SysUser sysUser : datas) {
                if (BaseConst._SUPER_ADMIN_LOGIN_CODE.equals(sysUser.getLoginCode())) {
                    continue;
                }
                String suffix = PinYinUtil.convertChineseToPinyinInitial(tenantName);
                log.info("{} 转换后的缩写：{}", tenantName , suffix);
                String loginCode = "Admin_" + suffix;
                String loginPassword = DigestUtils.sha256Hex("Admin@123_" + suffix);
                long uuid = ESUtil.getUUID();
                managerIds.add(String.valueOf(uuid));
                sysUser.setId(uuid);
                sysUser.setLoginCode(loginCode);
                sysUser.setLoginPasswd(loginPassword);
                sysUser.setDomainId(domainId);
                sysUser.setIcon(sysUser.getIcon());
                userList.add(sysUser);
            }
            userSvc.saveOrUpdateBatch(userList);
        }
        return managerIds;
    }

    private void rollbackDomainData(Long domainId) {
        BoolQueryBuilder tenantQuery = new BoolQueryBuilder();
        tenantQuery.must(QueryBuilders.termQuery("id", domainId));
        tenantDao.deleteByQuery(tenantQuery, true);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("domainId", domainId));
        List<SysRole> rolesByQuery = roleSvc.getRolesByQuery(boolQueryBuilder);
        if (!rolesByQuery.isEmpty()) {
            List<Long> roleIds = rolesByQuery.stream().map(SysRole::getId).toList();
            boolQueryBuilder.must(QueryBuilders.termsQuery("roleId", roleIds));
            esUserRoleRltSvc.deleteByQuery(boolQueryBuilder, true);
            roleIds.forEach(roleId -> {
                roleSvc.deleteById(roleId);
            });
        }
        BoolQueryBuilder orgQuery = new BoolQueryBuilder();
        orgQuery.must(QueryBuilders.termQuery("domainId", domainId));
        esUserOrgRltSvc.deleteByQuery(orgQuery, true);
        esOrgSvc.deleteByQuery(orgQuery, true);
    }

    private void filterManagers(Set<String> managers, Long managerRoleId, Long domainId) {
        CSysUserRoleRlt cdt = new CSysUserRoleRlt();
        cdt.setRoleId(managerRoleId);
        cdt.setDomainId(domainId);
        List<SysUserRoleRlt> rlts = userRoleRltSvc.getListByCdt(cdt);
        if (!rlts.isEmpty()) {
            for (SysUserRoleRlt rlt : rlts) {
                managers.remove(String.valueOf(rlt.getUserId()));
            }
        }
    }

    private void filterManagersForNoOrg(Set<String> managers, List<SysOrg> orgListByParentId, Long domainId) {
        CSysUserOrgRlt cSysUserOrgRlt = new CSysUserOrgRlt();
        cSysUserOrgRlt.setDomainId(domainId);
        cSysUserOrgRlt.setOrgIds(orgListByParentId.stream().map(SysOrg::getId).toArray(Long[]::new));
        esUserOrgRltSvc.getListByCdt(cSysUserOrgRlt).forEach(sysUserOrgRlt -> {
            managers.remove(sysUserOrgRlt.getUserId());
        });
    }

    private void initUinoSysDataModule(Long domainId) {
        List<SysDataModule> data = CommonFileUtil.getData("/initdata/uino_sys_data_module.json", SysDataModule.class);
        data.forEach(sysDataModule -> {
            sysDataModule.setDataSourceUrl(commUrl + sysDataModule.getDataSourceUrl());
            long uuid = ESUtil.getUUID();
            sysDataModule.setId(uuid);
            sysDataModule.setDomainId(domainId);
            sysDataModule.setCreateTime(System.currentTimeMillis());
            sysDataModule.setModifyTime(System.currentTimeMillis());
        });
        esDataModuleSvc.saveOrUpdateBatch(data);
    }

    private Map<Long, Long> initUinoSysModule(Long domainId) {
        List<SysModule> dates = CommonFileUtil.getData("/initdata/uino_sys_module.json", SysModule.class);
        //id映射，key：默认初始化id，value:新生成id
        Map<Long, Long> moduleIdMap = new HashMap<>();
        List<SysModule> sysModuleList = new ArrayList<>();
        for (SysModule sysModule : dates) {
            if (!"授权管理".equals(sysModule.getModuleName()) &&
                    !"租户管理".equals(sysModule.getModuleName())) {
                long uuid = ESUtil.getUUID();
                moduleIdMap.put(sysModule.getId(), uuid);
                sysModule.setId(uuid);
                sysModuleList.add(sysModule);
            }

        }
        int orderNum = 0;
        for (SysModule module : sysModuleList) {
            module.setOrderNo(++orderNum);
            module.setParentId(moduleIdMap.get(module.getParentId()));
            module.setDomainId(domainId);
            module.setIsInit(true);
        }
        esModuleSvc.saveOrUpdateBatch(sysModuleList);
        return moduleIdMap;
    }

    private Map<Long, Long> initOrgData(Long domainId, String tenantName) {
        List<SysOrg> dates = CommonFileUtil.getData("/initdata/uino_sys_org.json", SysOrg.class);
        Map<Long, Long> orgIdMap = new HashMap<>();
        dates.forEach(sysOrg -> {
            sysOrg.setId(domainId);
            sysOrg.setOrgCode(tenantName);
            sysOrg.setOrgName(tenantName);
            sysOrg.setDomainId(domainId);
            orgIdMap.put(sysOrg.getId(), domainId);
        });
        esOrgSvc.saveOrUpdateBatch(dates);
        return orgIdMap;
    }

    private Map<Long, Long> initUinoSysDictionaryClass(Long domainId) {
        Map<Long, Long> result = new HashMap<>();
        List<ESDictionaryClassInfo> dates = CommonFileUtil.getData("/initdata/uino_sys_dictionary_class.json",
                ESDictionaryClassInfo.class);
        for (ESDictionaryClassInfo dictClassInfo : dates) {
            long uuid = ESUtil.getUUID();
            result.put(dictClassInfo.getId(), uuid);
            dictClassInfo.setId(uuid);
            dictClassInfo.setDomainId(domainId);
            dictClassInfo.setIsInit(1);
            for (ESDictionaryAttrDef def : dictClassInfo.getDictAttrDefs()) {
                def.setDoaminId(domainId);
                if (BinaryUtils.isEmpty(def.getProStdName())) {
                    def.setProStdName(def.getProName());
                }
            }
        }
        //更新属性中引用字典分类Id
        for (ESDictionaryClassInfo dictClassInfo : dates) {
            for (ESDictionaryAttrDef def : dictClassInfo.getDictAttrDefs()) {
                if (def.getSourceDictClassId() != null) {
                    Long dictClasssId = result.get(def.getSourceDictClassId());
                    def.setSourceDictClassId(dictClasssId);
                }
            }
        }
        esDictionaryClassSvc.saveOrUpdateBatch(dates);
        return result;
    }

    private void initUinoSysDictionaryItem(Long domainId, Map<Long, Long> dictionaryClassMap) {
        List<ESDictionaryItemInfo> dates = CommonFileUtil.getData("/initdata/uino_sys_dictionary_item.json",
                ESDictionaryItemInfo.class);
        for (ESDictionaryItemInfo item : dates) {
            long uuid = ESUtil.getUUID();
            item.setId(uuid);
            item.setDomainId(domainId);
            item.setDictClassId(dictionaryClassMap.get(item.getDictClassId()));
            if (BinaryUtils.isEmpty(item.getOption())) {
                item.setOption(DictionaryOptionEnum.READ);
            }
        }
        esDictionaryItemSvc.saveOrUpdateBatch(dates);
    }

    private void initAppSquareConfigData(Long domainId) {
        List<AppSquareConfig> list = FileUtil.getData("/initdata/uino_eam_square_config.json", AppSquareConfig.class);
        List<AppSquareConfig> newList = list;
        // 判断文件是否存在
        URL resource = this.getClass().getClassLoader().getResource("./initdata/uino_business_config.json");
        if (resource != null) {
            List<String> businessConfigList = CommonFileUtil.getData("/initdata/uino_business_config.json", String.class);
            // 存在就过滤可用配置
            if (!CollectionUtils.isEmpty(businessConfigList)) {
                String result = businessConfigList.get(0);
                String content = Base64Util.base64dec(result);
                BusinessConfigDto businessConfigDto = JSONObject.parseObject(content, BusinessConfigDto.class);
                if (businessConfigDto != null && !CollectionUtils.isEmpty(businessConfigDto.getSquareConfig())) {
                    // 先删除初始化进去的系统模块数据
                    BoolQueryBuilder builder = QueryBuilders.boolQuery();
                    builder.must(QueryBuilders.termQuery("isInit", 0));
                    esAppSquareConfigDao.deleteByQuery(builder, true);
                    // 从功能配置读取数据
                    List<Long> configIdList = businessConfigDto.getSquareConfig();
                    list = list.stream().filter(appSquareConfig -> configIdList.contains(appSquareConfig.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(list)) {
                        list = newList;
                    }
                }
            }
        }
        try {
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(appSquareConfig -> {
                    Long uuid = ESUtil.getUUID();
                    appSquareConfig.setId(uuid);
                    appSquareConfig.setDomainId(domainId);
                });
                esAppSquareConfigDao.saveOrUpdateBatch(list);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void initSystemConfigData(Long domainId) {
        CcCiClassDir dir = new CcCiClassDir();
        dir.setIsLeaf(1);
        if (dir.getDirLvl() == null) {
            dir.setDirLvl(1);
        }
        dir.setDirName("架构设计工具系统核心配置");
        dir.setCiType(1);
        dir.setParentId(0L);
        dir.setDirPath("#" + ESUtil.getUUID() + "#");
        dir.setDomainId(domainId);
        Long bmDomainDirId = dirSvc.saveOrUpdate(dir);
        List<JSONObject> jsonData = FileUtil.getData("/initdata/uino_eam_system_config_class.json", JSONObject.class);
        List<CcCiClassInfo> datas = new ArrayList<>();
        for (JSONObject jsonDatum : jsonData) {
            CcCiClassInfo ccCiClassInfo = new CcCiClassInfo();
            CcCiClass ciClass = jsonDatum.getObject("ciClass", CcCiClass.class);
            ccCiClassInfo.setCiClass(ciClass);
            List<CcCiAttrDef> attrDefs = new ArrayList<>();
            JSONArray jsonArray = jsonDatum.getJSONArray("attrDefs");
            for (int i = 0; i < jsonArray.size(); i++) {
                ESCIAttrDefInfo object = jsonArray.getObject(i, ESCIAttrDefInfo.class);
                attrDefs.add(object);
            }
            ccCiClassInfo.setAttrDefs(attrDefs);
            datas.add(ccCiClassInfo);
        }
        if (!CollectionUtils.isEmpty(datas)) {
            datas.forEach(
                    ccCiClassInfo -> {
                        CcCiClass ciClass = ccCiClassInfo.getCiClass();
                        ciClass.setId(ESUtil.getUUID());
                        String iconUrl = httpResourceUrl + ciClass.getIcon();
                        ciClass.setIcon(iconUrl);
                        ciClass.setDomainId(domainId);
                        ciClass.setDirId(bmDomainDirId);
                    }
            );
            classSvc.saveOrUpdateBatch(domainId, datas);
        }
        DomainContextValue domainContextValue = DomainContext.getDomainContextValue();
        DomainReqType domainReqType = domainContextValue.getDomainReqType();
        Long oldDomainId = domainContextValue.getDomainId();
        domainContextValue.setDomainReqType(DomainReqType.FRONT);
        domainContextValue.setDomainId(domainId);
        try {
            JSONArray systemConfigJsonArr = FileUtil.readObject("/initdata/init_system_config.json", JSONArray.class);
            systemConfigJsonArr.forEach(next -> {
                JSONObject jsonObject1 = (JSONObject) next;
                String confType = jsonObject1.getString("CONF_TYPE");
                String confJson = jsonObject1.getString("CONF_JSON");
                String confName = jsonObject1.getString("CONF_NAME");
                bmConfigSvc.saveOrUpdateConfType(confType, confJson, confName, domainId);
            });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            domainContextValue.setDomainReqType(domainReqType);
            domainContextValue.setDomainId(oldDomainId);
        }
    }

    private void initResource(Long domainId) {
        File sourceFile = new File(localResourceUrl + "/122");
        String relativePath = domainId + "/" + "122.zip";
        File targetFile = new File(localResourceUrl + "/" + relativePath);
        if (!targetFile.getParentFile().exists()) {
            targetFile.getParentFile().mkdirs();
        }
        log.info("sourceFile: " + sourceFile.getAbsolutePath());
        log.info("targetFile: " + targetFile.getAbsolutePath());
        Compression.compressZip(sourceFile, targetFile, "UTF-8");
        Compression.uncompressZip(targetFile, targetFile.getParentFile());
        //同步资源
        iResourceSvc.saveSyncResourceInfo(relativePath, httpResourceUrl + relativePath, true, true, 1);
    }

    private Map<Long, Long> initUinoCiDir(Long domainId) {
        Map<Long, Long> result = new HashMap<>();
        List<CcCiClassDir> list = CommonFileUtil.getData("/initdata/uino_ci_dir.json", CcCiClassDir.class);
        list.forEach(ccCiClassDir -> {
            long uuid = ESUtil.getUUID();
            result.put(ccCiClassDir.getId(), uuid);
            ccCiClassDir.setDomainId(domainId);
            ccCiClassDir.setId(uuid);
            ccCiClassDir.setDirPath("#" + uuid + "#");
            ccCiClassDir.setCreateTime(System.currentTimeMillis());
            ccCiClassDir.setModifyTime(System.currentTimeMillis());
        });
        esDirSvc.saveOrUpdateBatch(list);
        return result;
    }

    private void initUinoCmdbImage(Long domainId, Map<Long, Long> dirMap) {
        List<CcImage> list = CommonFileUtil.getData("/initdata/uino_cmdb_image.json", CcImage.class);
        list.forEach(ccImage -> {
            ccImage.setDomainId(domainId);
            ccImage.setDirId(dirMap.get(ccImage.getDirId()));
            long uuid = ESUtil.getUUID();
            ccImage.setId(uuid);
            ccImage.setImgPath("/" + domainId + ccImage.getImgPath());
            ccImage.setCreateTime(System.currentTimeMillis());
            ccImage.setModifyTime(System.currentTimeMillis());
        });
        esImageSvc.saveOrUpdateBatch(list);
    }

    private void initDataSet() {

    }


    private void dealManagerRoleRlt(Set<String> managerIds, Long roleId, Long domainId) {
        List<SysUserRoleRlt> rltList = new ArrayList<>();
        for (String managerId : managerIds) {
            SysUserRoleRlt rlt = new SysUserRoleRlt();
            rlt.setId(ESUtil.getUUID());
            rlt.setUserId(Long.parseLong(managerId));
            rlt.setRoleId(roleId);
            rlt.setDomainId(domainId);
            rltList.add(rlt);
        }
        esUserRoleRltSvc.saveOrUpdateBatch(rltList);
    }

    private void dealManagerOrgRlt(Set<String> managerIds, Map<Long, Long> orgIdMap) {
        Set<Long> mangerIds = managerIds.stream()
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        List<SysUserOrgRlt> rltList = new ArrayList<>();
        orgIdMap.forEach((orgId, domainId) -> {
            mangerIds.forEach(userId -> {
                SysUserOrgRlt rltRequestDto = new SysUserOrgRlt();
                rltRequestDto.setDomainId(domainId);
                rltRequestDto.setUserId(userId);
                rltRequestDto.setOrgId(orgId);
                rltList.add(rltRequestDto);
            });
        });
        esUserOrgRltSvc.saveOrUpdateBatch(rltList);
    }

    private Map<String, Long> initRoleData(Long domainId) {
        Map<String, Long> roleIdMap = new HashMap<>();
        List<SysRole> data = CommonFileUtil.getData("/initdata/uino_sys_role.json", SysRole.class);
        for (SysRole sysRole : data) {
            long uuid = ESUtil.getUUID();
            roleIdMap.put(sysRole.getRoleName(), uuid);
            sysRole.setDomainId(domainId);
            sysRole.setId(uuid);
        }
        roleSvc.saveOrUpdateBatch(domainId, data);
        return roleIdMap;
    }

    private void dealRolePermission(Long roleId, Long domainId) {
        Assert.notNull(roleId, "角色ID不能为空");
        ModuleNodeInfo moduleTree = esModuleSvc.getModuleTree(domainId);
        List<Long> nodeIds = collectIds(moduleTree);
        List<SysRoleModuleRlt> rltList = new ArrayList<>();
        nodeIds.forEach(
                nodeId -> {
                    SysRoleModuleRlt sysRoleModuleRlt = new SysRoleModuleRlt();
                    sysRoleModuleRlt.setDomainId(domainId);
                    sysRoleModuleRlt.setRoleId(roleId);
                    sysRoleModuleRlt.setModuleId(nodeId);
                    rltList.add(sysRoleModuleRlt);
                }
        );
        roleApiSvc.addRoleMenuRlt(domainId, rltList);
        List<SysDataModule> allDataRoleMenu = roleApiSvc.getAllDataRoleMenu(domainId);
        if(!CollectionUtils.isEmpty(allDataRoleMenu)){
            allDataRoleMenu.forEach(dataModule -> {
                roleApiSvc.saveDateModule(dataModule, roleId, domainId);
            });
        }
    }

    public static List<Long> collectIds(ModuleNodeInfo node) {
        List<Long> ids = new ArrayList<>();
        if (node != null) {
            collectIdsRecursive(node, ids);
        }
        return ids;
    }

    /**
     * 递归方法，用于收集当前节点及其子节点的 id
     *
     * @param node 当前节点
     * @param ids  存储 id 的列表
     */
    private static void collectIdsRecursive(ModuleNodeInfo node, List<Long> ids) {
        if ("租户管理".equals(node.getLabel())) {
            return;
        }
        ids.add(node.getId());
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (ModuleNodeInfo child : node.getChildren()) {
                collectIdsRecursive(child, ids);
            }
        }
    }




}
