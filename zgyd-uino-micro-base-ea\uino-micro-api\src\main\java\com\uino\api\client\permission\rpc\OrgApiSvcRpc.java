package com.uino.api.client.permission.rpc;

import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.business.OrgNodeInfo;
import com.uino.bean.permission.business.request.*;
import com.uino.bean.permission.query.CSysOrg;
import com.uino.dao.BaseConst;
import com.uino.provider.feign.permission.OrgFeign;
import com.uino.api.client.permission.IOrgApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Primary
public class OrgApiSvcRpc implements IOrgApiSvc {

    @Autowired
    private OrgFeign orgFeign;

    @Override
    public OrgNodeInfo getOrgTree() {
        // TODO Auto-generated method stub
        return orgFeign.getOrgTree(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public OrgNodeInfo getOrgTree(Long domainId, Long rootOrg, boolean findUser) {
        return orgFeign.getOrgTree(domainId,rootOrg,findUser);
    }

    @Override
    public OrgNodeInfo getOrgTree(Long domainId) {
        return orgFeign.getOrgTree(domainId);
    }

    @Override
    public SysOrg getRootOrg(Long domainId) {
        return null;
    }

    @Override
    public List<SysOrg> getOrgListByParentId(Long parentId) {
        return Collections.emptyList();
    }

    @Override
	public OrgNodeInfo getOrgTreeV2(Long orgId) {
		// TODO Auto-generated method stub
		return orgFeign.getOrgTreeV2(BaseConst.DEFAULT_DOMAIN_ID,orgId);
	}

    @Override
    public OrgNodeInfo getOrgTreeV2(Long domainId, Long orgId) {
        return orgFeign.getOrgTreeV2(domainId,orgId);
    }

    @Override
    public OrgNodeInfo getOrgTreeV3(Long domainId) {
        return orgFeign.getOrgTreeV3(domainId);
    }

    @Override
    public List<OrgNodeInfo> getAllOrgTree(Set<Long> domainIds) {
        return Collections.emptyList();
    }

    @Override
    public Long saveOrUpdateOrg(SaveOrgRequestDto request) {
        // TODO Auto-generated method stub
        return orgFeign.saveOrUpdateOrg(request);
    }

    @Override
    public void deleteOrg(Set<Long> removeOrgIds) {
        // TODO Auto-generated method stub
        orgFeign.deleteOrg(removeOrgIds);
    }

    @Override
    public void addUserForOrg(AddOrRemoveUserToOrgRequestDto request) {
        // TODO Auto-generated method stub
        orgFeign.addUserForOrg(request);
    }

    @Override
    public void removeUserForOrg(AddOrRemoveUserToOrgRequestDto request) {
        // TODO Auto-generated method stub
        orgFeign.removeUserForOrg(request);
    }

    @Override
    public void addRoleForOrg(AddOrRemoveRoleToOrgRequestDto request) {
        // TODO Auto-generated method stub
        orgFeign.addRoleForOrg(request);
    }

    @Override
    public void removeRoleForOrg(AddOrRemoveRoleToOrgRequestDto request) {
        // TODO Auto-generated method stub
        orgFeign.removeRoleForOrg(request);
    }

    @Override
    public Set<Long> getUserIds(Long orgId) {
        // TODO Auto-generated method stub
        return orgFeign.getUserIds(orgId);
    }

    @Override
    public Set<Long> getRoleIds(Long orgId) {
        // TODO Auto-generated method stub
        return orgFeign.getRoleIds(orgId);
    }

    @Override
    public List<SysOrg> getOrgByRoleId(Long roleId) {
        // TODO Auto-generated method stub
        return orgFeign.getOrgByRoleId(roleId);
    }

    @Override
    public Page<SysOrg> queryPageByCdt(int pageNum, int pageSize, CSysOrg query) {
        // TODO Auto-generated method stub
        return orgFeign.queryPageByCdt(pageNum, pageSize, query);
    }

    @Override
    public List<SysOrg> queryListByCdt(CSysOrg query) {
        // TODO Auto-generated method stub
        return orgFeign.queryListByCdt(query);
    }

    @Override
    public void interchangeOrgNo(InterchangeOrgNoRequestDto reqDto) {
        // TODO Auto-generated method stub
        orgFeign.interchangeOrgNo(reqDto);
    }

    @Override
    public List<OrgNodeInfo> getTreeByCurrentUser() {
        return orgFeign.getTreeByCurrentUser();
    }

    @Override
    public void setUsersOrgs(SetUsersOrgsRequestDto reqDto) {
        // TODO Auto-generated method stub
        orgFeign.setUsersOrgs(reqDto);
    }
}
