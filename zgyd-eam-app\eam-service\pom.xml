<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.uinnova.product.eam</groupId>
		<artifactId>eam</artifactId>
		<version>fuxi-1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>eam-service</artifactId>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.uinnova.product.eam</groupId>
			<artifactId>eam-db</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>spring-cloud-netflix-hystrix-dashboard</artifactId>
					<groupId>org.springframework.cloud</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-cloud-starter-netflix-hystrix-dashboard</artifactId>
					<groupId>org.springframework.cloud</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.uinnova.product.eam</groupId>
			<artifactId>eam-model</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- ===================集成uino-micro-base项目依赖================== -->
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-api</artifactId>
			<version>${uino-micro-base.version}</version>
		</dependency>


		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.uinnova.product.eam</groupId>
			<artifactId>eam-workable-feign-client</artifactId>
			<version>fuxi-1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>${alibaba.druid.version}</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.github.librepdf/openpdf -->
		<dependency>
			<groupId>com.github.librepdf</groupId>
			<artifactId>openpdf</artifactId>
			<version>1.3.27</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
		</dependency>
		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>2.5.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
			<version>4.5.13</version>
		</dependency>

		<!-- GraalVM JavaScript 引擎 (推荐用于 JDK 17+) -->
		<dependency>
			<groupId>org.graalvm.js</groupId>
			<artifactId>js</artifactId>
			<version>22.3.3</version>
		</dependency>
		<dependency>
			<groupId>org.graalvm.js</groupId>
			<artifactId>js-scriptengine</artifactId>
			<version>22.3.3</version>
		</dependency>
	</dependencies>

</project>