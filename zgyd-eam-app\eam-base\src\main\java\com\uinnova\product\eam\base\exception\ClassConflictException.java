package com.uinnova.product.eam.base.exception;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025/7/21 13:41
 */
public class ClassConflictException extends RuntimeException{


    public ClassConflictException() {
    }

    public ClassConflictException(String message) {
        super(message);
    }

    public ClassConflictException(String message, Throwable cause) {
        super(message, cause);
    }

    public ClassConflictException(Throwable cause) {
        super(cause);
    }

    public ClassConflictException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
