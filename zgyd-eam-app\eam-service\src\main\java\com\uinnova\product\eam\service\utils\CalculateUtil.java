package com.uinnova.product.eam.service.utils;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.model.cj.enums.FormulaTypeEnum;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.PropertyType;
import com.uino.bean.cmdb.base.ESCIAttrTransConfig;
import lombok.extern.slf4j.Slf4j;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * 计算工具类
 * <AUTHOR>
 */
@Slf4j
public class CalculateUtil {

    public static BigDecimal calculate(String formula, Map<String, Object> attrs, String formulaType, Map<String, CcCiAttrDef> defMap, Map<Long, ESCIAttrTransConfig> attrConfigMap) {

        if(BinaryUtils.isEmpty(formula)){
            return BigDecimal.valueOf(0).setScale(2);
        }
        for (Map.Entry<String, Object> entry : attrs.entrySet()) {
            PropertyType type = null;
            CcCiAttrDef def = defMap.get(entry.getKey());
            if (!BinaryUtils.isEmpty(def)) {
                Integer proType = def.getProType();
                ESCIAttrTransConfig transConfig = attrConfigMap.get(def.getId());
                if (transConfig != null && transConfig.getTargetAttrType() != null) {
                    proType = transConfig.getTargetAttrType();
                }
                type = PropertyType.valueOf(proType);
            }
            String value = String.valueOf(entry.getValue());
            try {
                if(value.contains("%")){
                    value = value.replace("%","").trim();
                    value = String.valueOf(Double.parseDouble(value) / 100);
                }
                if(PropertyType.PERCENT.equals(type)){
                    value = String.valueOf(Double.parseDouble(value) / 100);
                }
                Double.parseDouble(value);
            } catch (NumberFormatException e) {
                value = "0";
            }
            if(FormulaTypeEnum.CALCULATE.getType().equals(formulaType)){
                //计算属性根据配置约束
                formula = formula.replace("${" + entry.getKey() + "}", value);
            }

        }

        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = getJavaScriptEngine(manager);

        if (engine == null) {
            log.error("JavaScript引擎不可用，无法执行公式计算: {}", formula);
            log.error("请确保已添加JavaScript引擎依赖，如GraalVM JS或Rhino");
            return BigDecimal.valueOf(0).setScale(2);
        }

        try {
            Object result = engine.eval(formula);
            if (result == null) {
                return BigDecimal.valueOf(0).setScale(2);
            }
            BigDecimal calculatedResult = new BigDecimal(result.toString());
            return calculatedResult.setScale(2, RoundingMode.HALF_UP);
        } catch (ScriptException e) {
            log.error("公式计算执行失败: {}, 错误: {}", formula, e.getMessage());
        } catch (NumberFormatException e) {
            log.error("公式计算结果转换为数字失败: {}, 错误: {}", formula, e.getMessage());
        }
        return BigDecimal.valueOf(0).setScale(2);
    }

    public static Boolean judge(String expression) {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = getJavaScriptEngine(manager);

        if (engine == null) {
            log.error("JavaScript引擎不可用，无法执行表达式判断: {}", expression);
            log.error("请确保已添加JavaScript引擎依赖，如GraalVM JS或Rhino");
            return false;
        }

        try {
            Object result = engine.eval(expression);
            if (result == null) {
                return false;
            }
            return (Boolean) result;
        } catch (ScriptException e) {
            log.error("表达式判断执行失败: {}, 错误: {}", expression, e.getMessage());
        } catch (ClassCastException e) {
            log.error("表达式判断结果转换为布尔值失败: {}, 错误: {}", expression, e.getMessage());
        }
        return false;
    }

    /**
     * 获取JavaScript引擎，支持多种引擎类型
     *
     * @param manager ScriptEngineManager
     * @return JavaScript引擎，如果都不可用则返回null
     */
    private static ScriptEngine getJavaScriptEngine(ScriptEngineManager manager) {
        // 按优先级尝试不同的JavaScript引擎
        String[] engineNames = {
            "graal.js",        // GraalVM JavaScript (推荐用于JDK 17+)
            "js",              // 通用JavaScript引擎名称
            "JavaScript",      // 标准JavaScript引擎名称
            "nashorn",         // Oracle Nashorn (JDK 8-14)
            "rhino"            // Mozilla Rhino
        };

        for (String engineName : engineNames) {
            try {
                ScriptEngine engine = manager.getEngineByName(engineName);
                if (engine != null) {
                    log.info("使用JavaScript引擎: {}", engineName);
                    return engine;
                }
            } catch (Exception e) {
                // 忽略异常，继续尝试下一个引擎
                log.info("尝试引擎 {} 失败: {}", engineName, e.getMessage());
            }
        }

        // 打印可用的引擎列表用于调试
        log.info("未找到可用的JavaScript引擎，请确保已添加相应依赖。");
        log.info("可用的脚本引擎列表:");
        manager.getEngineFactories().forEach(factory -> {
            log.info("  引擎名称: {}, 语言: {}, 别名: {}", factory.getEngineName(), factory.getLanguageName(), factory.getNames());
        });

        return null;
    }
}
