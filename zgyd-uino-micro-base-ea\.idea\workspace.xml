<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="105ecd6a-4bc3-46b8-a729-24a1dda6b832" name="更改" comment="feat：分类添加不校验属性的批量保存方法&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="zgyd-zuhu-lichong" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zqdJrvaI7n8p2Td7Sm3De5LdEj" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.eam-base [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.eam-base [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.uino-eam-micro-service [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.uino-eam-micro-service [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;zgyd-zuhu&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/workspace/mycode/zgyd/zgyd-uino-micro-base-ea&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;
  }
}</component>
  <component name="RunManager">
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="zgyd-uino-micro-base-ea" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <module name="zgyd-uino-micro-base-ea" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="105ecd6a-4bc3-46b8-a729-24a1dda6b832" name="更改" comment="" />
      <created>1752462468993</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752462468993</updated>
      <workItem from="1752462470523" duration="6510000" />
      <workItem from="1752560203295" duration="20968000" />
      <workItem from="1752740737931" duration="5959000" />
      <workItem from="1752820073626" duration="4022000" />
      <workItem from="1753065034983" duration="29384000" />
      <workItem from="1753413605197" duration="6710000" />
    </task>
    <task id="LOCAL-00001" summary="feat：添加获取当前用户根组织接口，适配元模型多租户改造&#10;fix：修复角色接口多租户环境免密接口报错问题">
      <option name="closed" value="true" />
      <created>1752567253236</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752567253236</updated>
    </task>
    <task id="LOCAL-00002" summary="fix：修复数据超市多租户数据构建bug">
      <option name="closed" value="true" />
      <created>1752630717558</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752630717558</updated>
    </task>
    <task id="LOCAL-00003" summary="feat：分类添加当前分类是否来源集团和rootId字段&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752809354555</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752809354555</updated>
    </task>
    <task id="LOCAL-00004" summary="feat：调整查询逻辑，查询时过滤掉参考元模型&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752820125429</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752820125429</updated>
    </task>
    <task id="LOCAL-00005" summary="feat：分类添加标准类型字段，默认为非标准&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752835117078</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752835117078</updated>
    </task>
    <task id="LOCAL-00006" summary="feat：元模型新建时添加creator字段&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753152368099</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753152368099</updated>
    </task>
    <task id="LOCAL-00007" summary="feat：元模型添加是否更新提示字段&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753165542982</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753165542982</updated>
    </task>
    <task id="LOCAL-00008" summary="feat：分类添加不校验属性的批量保存方法&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753414537715</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753414537715</updated>
    </task>
    <option name="localTasksCounter" value="9" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="781a970a-aab7-408b-a16a-3ebae9885120" value="TOOL_WINDOW" />
        <entry key="e63cdf54-26ab-452d-ac53-54556ad12776" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="781a970a-aab7-408b-a16a-3ebae9885120">
          <value>
            <State />
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="zgyd-zuhu" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="e63cdf54-26ab-452d-ac53-54556ad12776">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="feat：添加获取当前用户根组织接口，适配元模型多租户改造&#10;fix：修复角色接口多租户环境免密接口报错问题" />
    <MESSAGE value="fix：修复数据超市多租户数据构建bug" />
    <MESSAGE value="aa" />
    <MESSAGE value="feat：分类添加当前分类是否来源集团和rootId字段&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：调整查询逻辑，查询时过滤掉参考元模型&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：分类添加标准类型字段，默认为非标准&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：元模型新建时添加creator字段&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：元模型添加是否更新提示字段&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：分类添加不校验属性的批量保存方法&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <option name="LAST_COMMIT_MESSAGE" value="feat：分类添加不校验属性的批量保存方法&#10;tapd：【元模型管理改造】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/uino-micro-service/src/main/java/com/uino/service/cmdb/microservice/impl/ImageSvc.java</url>
          <line>585</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>