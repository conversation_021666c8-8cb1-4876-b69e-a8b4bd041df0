package com.uino.provider.feign.permission;

import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.business.OrgNodeInfo;
import com.uino.bean.permission.business.request.*;
import com.uino.bean.permission.query.CSysOrg;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/org", configuration = {
        BaseFeignConfig.class})
public interface OrgFeign {

    /**
     * 获取组织tree
     * 
     * @return
     */
    @PostMapping("getOrgTree")
    public OrgNodeInfo getOrgTree(Long domainId);

    public OrgNodeInfo getOrgTree(Long domainId, Long rootOrg, boolean findUser);

    /**
	 * 根据数据量动态获取组织tree
	 * @param orgId 父级组织id
	 */
    @PostMapping("getOrgTreeV2")
	public OrgNodeInfo getOrgTreeV2(@RequestParam(value = "domainId") Long domainId,@RequestBody(required = false) Long orgId);

    public OrgNodeInfo getOrgTreeV3(Long domainId);
    /**
     * 持久化组织
     * 
     * @param request
     * @return
     */
    @PostMapping("saveOrUpdateOrg")
    public Long saveOrUpdateOrg(@RequestBody(required = false) SaveOrgRequestDto request);

    /**
     * 移除组织
     * 
     * @param removeOrgIds
     */
    @PostMapping("deleteOrg")
    public void deleteOrg(@RequestBody(required = false) Set<Long> removeOrgIds);

    /**
     * 向某个组织下添加用户
     * 
     * @param request
     */
    @PostMapping("addUserForOrg")
    public void addUserForOrg(@RequestBody(required = false) AddOrRemoveUserToOrgRequestDto request);

    /**
     * 将用户从某组织下移除
     * 
     * @param request
     */
    @PostMapping("removeUserForOrg")
    public void removeUserForOrg(@RequestBody(required = false) AddOrRemoveUserToOrgRequestDto request);

    /**
     * 向某个组织绑定角色
     * 
     * @param request
     */
    @PostMapping("addRoleForOrg")
    public void addRoleForOrg(@RequestBody(required = false) AddOrRemoveRoleToOrgRequestDto request);

    /**
     * 将某些角色从组织下移除
     * 
     * @param request
     */
    @PostMapping("removeRoleForOrg")
    public void removeRoleForOrg(@RequestBody(required = false) AddOrRemoveRoleToOrgRequestDto request);

    /**
     * 获取指定组织下的用户
     * 
     * @param orgId
     * @return
     */
    @PostMapping("getUserIds")
    public Set<Long> getUserIds(@RequestParam(value = "orgId", required = false) Long orgId);

    /**
     * 获取指定组织下的角色
     * 
     * @param orgId
     * @return
     */
    @PostMapping("getRoleIds")
    public Set<Long> getRoleIds(@RequestParam(value = "orgId", required = false) Long orgId);


    @PostMapping("getOrgByRoleId")
    public List<SysOrg> getOrgByRoleId(@RequestBody Long roleId);

    /**
     * 根据查询条件分页查询组织
     * 
     * @param pageNum
     * @param pageSize
     * @param query
     * @return
     */
    @PostMapping("queryPageByCdt")
    public Page<SysOrg> queryPageByCdt(@RequestParam(value = "pageNum", required = false) int pageNum,
            @RequestParam(value = "pageSize", required = false) int pageSize,
            @RequestBody(required = false) CSysOrg query);

    /**
     * 不分页条件查询组织
     * 
     * @param query
     * @return
     */
    @PostMapping("queryListByCdt")
    public List<SysOrg> queryListByCdt(@RequestBody(required = false) CSysOrg query);

    @PostMapping("interchangeOrgNo")
    public void interchangeOrgNo(@RequestBody(required = false) InterchangeOrgNoRequestDto reqDto);

    @PostMapping("setUsersOrgs")
    public void setUsersOrgs(@RequestBody(required = false) SetUsersOrgsRequestDto reqDto);

    @PostMapping("getTreeByCurrentUser")
    public List<OrgNodeInfo> getTreeByCurrentUser();
}
