package com.uinnova.product.eam.init;

import com.uinnova.product.eam.comm.model.es.EamTenant;
import com.uinnova.product.eam.service.es.EamTenantDao;
import com.uino.api.client.permission.IRoleApiSvc;
import com.uino.bean.permission.base.*;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.permission.*;
import com.uino.dao.permission.rlt.ESUserOrgRltSvc;
import com.uino.dao.permission.rlt.ESUserRoleRltSvc;
import com.uino.dao.saas.constant.SaaSConstant;
import com.uino.dao.util.ESUtil;
import com.uino.service.permission.microservice.impl.UserSvc;
import com.uino.util.sys.CommonFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @FileName: InitPlatFormManager
 * @Desc:
 * @Author: WangBaoDe
 * @Date: 2025/07/09 17:49
 **/

@Component
@Order(100)
@Slf4j
public class InitPlatFormManager implements ApplicationRunner {

    private static final Long PLAT_FORM_MANAGER_DOMAIN_ID = 0L;

    @Autowired
    private ESRoleSvc roleSvc;

    @Autowired
    private ESUserRoleRltSvc esUserRoleRltSvc;
    @Autowired
    private ESDataModuleSvc esDataModuleSvc;

    @Autowired
    private ESUserOrgRltSvc esUserOrgRltSvc;

    @Autowired
    private EamTenantDao tenantDao;

    @Autowired
    private ESModuleSvc esModuleSvc;

    @Autowired
    private IRoleApiSvc roleApiSvc;

    @Autowired
    private ESUserSvc esUserSvc;

    @Autowired
    private ESOrgSvc esOrgSvc;

    @Autowired
    private UserSvc userSvc;

    @Value("${permission.http.prefix:}")
    private String commUrl;

    @Value("${permission.default.manager.permission.module:QuickEa;设置;租户管理;用户管理;授权管理;LDAP集成}")
    private String defaultManagerPermissionModule;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("初始化平台管理员角色权限开始...");
        try {
            initDefaultTenant();
            Map<Long, String> userMap = initPlatFormManagerUser();
            Set<String> defaultManagerLoginCode = new HashSet<>(userMap.values());
            Set<Long> managerIds = new HashSet<>(userMap.keySet());
            initPlatFormManagerTenant(managerIds);
            BoolQueryBuilder rltQuery = new BoolQueryBuilder();
            rltQuery.must(QueryBuilders.termQuery("roleId", SaaSConstant.PLATFORM_ADMIN_ROLE_ID));
            List<SysUserRoleRlt> rltList = esUserRoleRltSvc.getListByQuery(rltQuery);
            if (CollectionUtils.isEmpty(rltList)) {
                CSysUser cSysUser = new CSysUser();
                cSysUser.setLoginCodes(defaultManagerLoginCode.toArray(new String[]{}));
                cSysUser.setDomainId(PLAT_FORM_MANAGER_DOMAIN_ID);
                List<UserInfo> userInfoByLoginCode = userSvc.getUserInfoByCdt(cSysUser, false);
                if (CollectionUtils.isEmpty(userInfoByLoginCode)) {
                    log.error("用户不存在: {}", defaultManagerLoginCode);
                }
                List<SysUserRoleRlt> roleRlts = new ArrayList<>();
                for (SysUser manager : userInfoByLoginCode) {
                    SysUserRoleRlt rlt = new SysUserRoleRlt();
                    rlt.setId(ESUtil.getUUID());
                    rlt.setUserId(manager.getId());
                    rlt.setRoleId(SaaSConstant.PLATFORM_ADMIN_ROLE_ID);
                    rlt.setDomainId(PLAT_FORM_MANAGER_DOMAIN_ID);
                    roleRlts.add(rlt);
                }
                esUserRoleRltSvc.saveOrUpdateBatch(roleRlts);
                log.info("初始化平台管理员角色权限成功： " + defaultManagerLoginCode);
            }
        } catch (Exception e) {
            rollbackPlatFormManagerTenant(PLAT_FORM_MANAGER_DOMAIN_ID);
            log.error("初始化平台管理员角色权限失败： " + e);
            e.printStackTrace();
        }
    }

    private void initDefaultTenant() {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("id", SaaSConstant.GROUP_DOMAIN_ID));
        List<EamTenant> tenants = tenantDao.getListByQuery(boolQueryBuilder);
        if (CollectionUtils.isEmpty(tenants)) {
            EamTenant eamTenant = new EamTenant();
            eamTenant.setId(SaaSConstant.GROUP_DOMAIN_ID);
            eamTenant.setName("集团");
            eamTenant.setTenantId("Tenant-default");
            eamTenant.setDataStatus(1);
            tenantDao.saveOrUpdate(eamTenant);
            log.info("初始化默认租户成功： " + eamTenant.getName());
        }
    }

    private void initPlatFormManagerTenant(Set<Long> managerIds) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("id", PLAT_FORM_MANAGER_DOMAIN_ID));
        List<EamTenant> tenants = tenantDao.getListByQuery(boolQueryBuilder);
        if (CollectionUtils.isEmpty(tenants)) {
            EamTenant eamTenant = new EamTenant();
            eamTenant.setId(PLAT_FORM_MANAGER_DOMAIN_ID);
            eamTenant.setName("租户_平台管理");
            eamTenant.setTenantId("Tenant-manager");
            eamTenant.setDataStatus(1);
            tenantDao.saveOrUpdate(eamTenant);
            initUinoSysDataModule(PLAT_FORM_MANAGER_DOMAIN_ID);
            initUinoSysModule(PLAT_FORM_MANAGER_DOMAIN_ID);
            Map<Long, Long> orgData = initOrgData(PLAT_FORM_MANAGER_DOMAIN_ID);
            dealManagerOrgRlt(managerIds, orgData);
            initRole();
            log.info("初始化管理员租户成功： " + eamTenant.getName());
        }
    }

    private void rollbackPlatFormManagerTenant(Long domainId){
        //租户
        tenantDao.deleteById(domainId);
        //角色
        roleSvc.deleteRoleById(SaaSConstant.PLATFORM_ADMIN_ROLE_ID);
        //租户菜单
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        esModuleSvc.deleteByQuery(query, true);
        //数据模块
        esDataModuleSvc.deleteByQuery(query, true);
        //组织
        esOrgSvc.deleteByQuery(query, true);
    }
    private void initRole() {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("id", SaaSConstant.PLATFORM_ADMIN_ROLE_ID));
        List<SysRole> rolesByQuery = roleSvc.getListByQuery(boolQueryBuilder);
        if (CollectionUtils.isEmpty(rolesByQuery)) {
            SysRole sysRole = new SysRole();
            sysRole.setId(SaaSConstant.PLATFORM_ADMIN_ROLE_ID);
            sysRole.setRoleName(SaaSConstant.PLATFORM_ADMIN_ROLE_NAME);
            sysRole.setDomainId(PLAT_FORM_MANAGER_DOMAIN_ID);
            sysRole.setRoleDesc("平台管理员角色");
            sysRole.setStatus(1);
            roleSvc.saveOrUpdate(sysRole);
            initRolePermission();
        }
    }

    private void dealManagerOrgRlt(Set<Long> managerIds, Map<Long, Long> orgIdMap) {
        List<SysUserOrgRlt> rltList = new ArrayList<>();
        orgIdMap.forEach((orgId, domainId) -> {
            managerIds.forEach(userId -> {
                SysUserOrgRlt rltRequestDto = new SysUserOrgRlt();
                rltRequestDto.setDomainId(domainId);
                rltRequestDto.setUserId(userId);
                rltRequestDto.setOrgId(orgId);
                rltList.add(rltRequestDto);
            });
        });
        esUserOrgRltSvc.saveOrUpdateBatch(rltList);
    }

    private Map<Long, String> initPlatFormManagerUser() {
        Map<Long,String> userMap = new HashMap<>();
        List<SysUser> datas = CommonFileUtil.getData("/initdata/uino_sys_platform_user.json",
                SysUser.class);
        if (!CollectionUtils.isEmpty(datas)) {
            datas.forEach(user -> {
                userMap.put(user.getId(), user.getLoginCode());
            });
            List<SysUser> userList = new ArrayList<>(datas);
            esUserSvc.saveOrUpdateBatch(userList);
        }
        return userMap;
    }

    private void initUinoSysDataModule(Long domainId) {
        List<SysDataModule> data = CommonFileUtil.getData("/initdata/uino_sys_data_module.json", SysDataModule.class);
        data.forEach(sysDataModule -> {
            sysDataModule.setDataSourceUrl(commUrl + sysDataModule.getDataSourceUrl());
            long uuid = ESUtil.getUUID();
            sysDataModule.setId(uuid);
            sysDataModule.setDomainId(domainId);
            sysDataModule.setCreateTime(System.currentTimeMillis());
            sysDataModule.setModifyTime(System.currentTimeMillis());
        });
        esDataModuleSvc.saveOrUpdateBatch(data);
    }

    private Map<Long, Long> initUinoSysModule(Long domainId) {
        List<SysModule> dates = CommonFileUtil.getData("/initdata/uino_sys_module.json", SysModule.class);
        //id映射，key：默认初始化id，value:新生成id
        Map<Long, Long> moduleIdMap = new HashMap<>();
        List<SysModule> sysModuleList = new ArrayList<>();
        Set<String> permissionModules = new HashSet<>(Arrays.asList(defaultManagerPermissionModule.split(";")));
        for (SysModule sysModule : dates) {
            if(permissionModules.contains(sysModule.getModuleName())){
                long uuid = ESUtil.getUUID();
                moduleIdMap.put(sysModule.getId(), uuid);
                sysModule.setId(uuid);
                sysModuleList.add(sysModule);
            }
        }
        int orderNum = 0;
        for (SysModule module : sysModuleList) {
            module.setOrderNo(++orderNum);
            module.setParentId(moduleIdMap.get(module.getParentId()));
            module.setDomainId(domainId);
            module.setIsInit(true);
        }
        esModuleSvc.saveOrUpdateBatch(sysModuleList);
        return moduleIdMap;
    }

    private Map<Long, Long> initOrgData(Long domainId) {
        List<SysOrg> dates = CommonFileUtil.getData("/initdata/uino_sys_org.json", SysOrg.class);
        Map<Long, Long> orgIdMap = new HashMap<>();
        dates.forEach(sysOrg -> {
            sysOrg.setId(domainId);
            sysOrg.setOrgName("租户_平台管理");
            sysOrg.setOrgCode("租户_平台管理");
            sysOrg.setDomainId(domainId);
            orgIdMap.put(sysOrg.getId(), domainId);
        });
        esOrgSvc.saveOrUpdateBatch(dates);
        return orgIdMap;
    }


    private void initRolePermission() {
//        ModuleNodeInfo moduleTree = esModuleSvc.getModuleTree(PLAT_FORM_MANAGER_DOMAIN_ID);
//        List<Long> nodeIds = collectIds(moduleTree, defaultManagerPermissionModule);
        List<SysModule> allModules = esModuleSvc.getAll(PLAT_FORM_MANAGER_DOMAIN_ID);
        List<Long> nodeIds = allModules.stream().map(SysModule::getId).toList();
        List<SysRoleModuleRlt> rltList = new ArrayList<>();
        nodeIds.forEach(
                nodeId -> {
                    SysRoleModuleRlt sysRoleModuleRlt = new SysRoleModuleRlt();
                    sysRoleModuleRlt.setDomainId(PLAT_FORM_MANAGER_DOMAIN_ID);
                    sysRoleModuleRlt.setRoleId(SaaSConstant.PLATFORM_ADMIN_ROLE_ID);
                    sysRoleModuleRlt.setModuleId(nodeId);
                    rltList.add(sysRoleModuleRlt);
                }
        );
        roleApiSvc.addRoleMenuRlt(PLAT_FORM_MANAGER_DOMAIN_ID, rltList);

        List<SysDataModule> allDataRoleMenu = roleApiSvc.getAllDataRoleMenu(PLAT_FORM_MANAGER_DOMAIN_ID);
        if (!CollectionUtils.isEmpty(allDataRoleMenu)) {
            allDataRoleMenu.forEach(dataModule -> {
                roleApiSvc.saveDateModule(dataModule, SaaSConstant.PLATFORM_ADMIN_ROLE_ID, PLAT_FORM_MANAGER_DOMAIN_ID);
            });
        }
    }

    private static Map<Long, ModuleNodeInfo> buildModuleNodeMap(ModuleNodeInfo root) {
        Map<Long, ModuleNodeInfo> nodeMap = new HashMap<>();
        if (root != null) {
            buildNodeMapRecursive(root, nodeMap);
        }
        return nodeMap;
    }

    private static void buildNodeMapRecursive(ModuleNodeInfo node, Map<Long, ModuleNodeInfo> nodeMap) {
        nodeMap.put(node.getId(), node);
        if (node.getChildren() != null) {
            for (ModuleNodeInfo child : node.getChildren()) {
                buildNodeMapRecursive(child, nodeMap);
            }
        }
    }


    public static List<Long> collectIds(ModuleNodeInfo node, String defaultManagerPermissionModule) {
        Set<Long> ids = new LinkedHashSet<>();
        Set<String> permissionModules = new HashSet<>(Arrays.asList(defaultManagerPermissionModule.split(";")));
        Map<Long, ModuleNodeInfo> nodeMap = buildModuleNodeMap(node);
        // 递归遍历整棵树，找到 label 匹配的节点
        findMatchedNodesAndCollectParents(node, permissionModules, ids, nodeMap);
        return new ArrayList<>(ids);
    }

    private static void findMatchedNodesAndCollectParents(
            ModuleNodeInfo node,
            Set<String> permissionModules,
            Set<Long> ids,
            Map<Long, ModuleNodeInfo> nodeMap) {
        if (node == null) return;
        if (permissionModules.contains(node.getLabel())) {
            collectParentIds(node, nodeMap, ids);
        }
        if (node.getChildren() != null) {
            for (ModuleNodeInfo child : node.getChildren()) {
                findMatchedNodesAndCollectParents(child, permissionModules, ids, nodeMap);
            }
        }
    }

    private static void collectParentIds(ModuleNodeInfo node, Map<Long, ModuleNodeInfo> nodeMap, Set<Long> ids) {
        ModuleNodeInfo current = node;
        while (current != null) {
            ids.add(current.getId());
            Long parentId = current.getParentId();
            if (parentId == null || parentId == 0L) break;
            current = nodeMap.get(parentId);
        }
    }

}
