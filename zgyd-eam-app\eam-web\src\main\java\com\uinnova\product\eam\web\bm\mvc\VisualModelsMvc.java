package com.uinnova.product.eam.web.bm.mvc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.exception.ClassConflictException;
import com.uinnova.product.eam.feign.workable.entity.TaskRequest;
import com.uinnova.product.eam.model.dto.VisualModelDiffDto;
import com.uinnova.product.eam.model.dto.VisualModelDiffResultDto;
import com.uinnova.product.eam.model.dto.VisualModelsDto;
import com.uinnova.product.eam.model.vo.VisualModelsVo;
import com.uinnova.product.eam.web.bm.peer.VisualModelsPeer;
import com.uinnova.product.eam.comm.model.es.ModelPanorama3D;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.util.sys.SysUtil;
import com.uino.bean.cmdb.base.ClassInfoHistory;
import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.bean.cmdb.base.RltInfoHistory;
import com.uino.bean.permission.base.SysUser;

import org.apache.commons.lang3.StringUtils;
import com.uino.web.auth.VerifyAuthUtil;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 元模型
 * @author: Lc
 * @create: 2021-11-04 10:01
 */
@RestController
@RequestMapping("/bm/visual/model")
public class VisualModelsMvc {

    @Resource
    private VisualModelsPeer visualModelsPeer;

    @Resource
    private VerifyAuthUtil verifyAuth;

    public static final String SYS_MODULE_SIGN = "元模型管理";
    public static final String SYS_MODULE_SIGN_NEW = "元模型管理新版";


    @PostMapping("/handlerVisualModels")
    public RemoteResult handlerVisualModels(@RequestBody String body) {
        JSONObject json = JSON.parseObject(body);
        Long modelId = json.getLong("id");
        // 状态，0：取消编辑  1：编辑
        Integer status = json.getInteger("status");
        Map<String, Object> result = visualModelsPeer.handlerVisualModels(modelId, status);
        return new RemoteResult(result);
    }


    @PostMapping("queryVisualModels")
    public void queryVisualModels(@RequestBody String body,
                                  HttpServletRequest request,
                                  HttpServletResponse response) {
        List<Long> sheetIds = (List)JSONArray.parseArray(body);
        SysUser user = SysUtil.getCurrentUserInfo();
        List<VisualModelsVo> models = visualModelsPeer.queryVisualModels(sheetIds, user.getDomainId());
        ControllerUtils.returnJson(request, response, models);
    }

    @GetMapping("/exportVisualModels")
    public RemoteResult exportVisualModels() {
        boolean result = visualModelsPeer.exportVisualModels();
        return new RemoteResult(result);
    }

    @PostMapping("/importVisualModels")
    public RemoteResult importVisualModels(@RequestBody VisualModelsDto dto) {
        boolean result = visualModelsPeer.importVisualModels(dto);
        return new RemoteResult(result);
    }

    /**
     * 保存可视化模型
     * @param model
     * @return
     */
    @PostMapping("/saveVisualModel")
    @ModDesc(desc = "保存可视化模型", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "可视化模型id", rType = Long.class)
    public RemoteResult saveVisualModel(@RequestBody ESVisualModel model) {
        Long status = visualModelsPeer.saveVisualModel(model);
        return new RemoteResult(status);
    }

    /**
     * 获取第二级组织列表
     */
    @GetMapping("/getVisualModelOrgList")
    @ModDesc(desc = "保存可视化模型", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "可视化模型数据", rType = Long.class)
    public RemoteResult getVisualModelOrgList() {
        return new RemoteResult(visualModelsPeer.getVisualModelOrgList());
    }

    /**
     * 保存可视化模型私有库
     * @param model
     * @return
     */
    @PostMapping("/saveVisualModelPrivate")
    @ModDesc(desc = "保存可视化模型", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "可视化模型数据", rType = Long.class)
    public RemoteResult saveVisualModelPrivate(@RequestBody ESVisualModel model) {
        // 兼容不同的菜单数据
        List<String> moduleSigns = new java.util.ArrayList<>();
        moduleSigns.add(SYS_MODULE_SIGN);
        moduleSigns.add(SYS_MODULE_SIGN_NEW);
        verifyAuth.verifyAuth(moduleSigns);
        Long status = visualModelsPeer.saveVisualModelPrivate(model);
        return new RemoteResult(status);
    }

    /**
     * 发布可视化模型
     * @param model
     * @return
     */
    @PostMapping("/publishVisualModel")
    @ModDesc(desc = "发布可视化模型", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "可视化模型id", rType = Long.class)
    public RemoteResult publishVisualModel(@RequestBody ESVisualModel model) {
        Long status = visualModelsPeer.publishVisualModel(model.getId(),"");
        return new RemoteResult(status);
    }

    /**
     * 获取当前用户元模型列表,本地和发布后的
     * @return
     */
    @GetMapping("/getVisualModelList")
    @ModDesc(desc = "获取元模型列表", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "可视化模型id", rType = Long.class)
    public RemoteResult getVisualModelList() {
        // 兼容不同的菜单数据
        List<String> moduleSigns = new java.util.ArrayList<>();
        moduleSigns.add(SYS_MODULE_SIGN);
        moduleSigns.add(SYS_MODULE_SIGN_NEW);
        verifyAuth.verifyAuth(moduleSigns);
        return new RemoteResult(visualModelsPeer.getVisualModelList());
    }

    @GetMapping("/getPrivateVisualModelIdByDesignId")
    @ModDesc(desc = "根据发布元模型id获取当前用户本地元模型id", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "可视化模型id", rType = Long.class)
    public RemoteResult getVisualModelPrivate(Long designVisId) {
        return new RemoteResult(visualModelsPeer.getVisualModelPrivate(designVisId));
    }

    @GetMapping("/getPrivateVisualModelById")
    @ModDesc(desc = "根据元模型id获取元模型详情信息", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "可视化模型id", rType = Long.class)
    public RemoteResult getVisualModelPrivateById(Long privateVisId) {
        return new RemoteResult(visualModelsPeer.getVisualModelPrivateById(privateVisId));
    }

    @GetMapping("/getVisualModelById")
    @ModDesc(desc = "根据元模型id获取元模型详情信息", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "可视化模型id", rType = Long.class)
    public RemoteResult getVisualModelById(Long visualModelId) {
        return new RemoteResult(visualModelsPeer.getVisualModelById(visualModelId));
    }

    @PostMapping("/convertVisualModelToExample")
    public RemoteResult convertVisualModelToExample(@RequestBody ESVisualModel model) {
        Long l = visualModelsPeer.convertVisualModelToExample(model.getId());
        return new RemoteResult(l);
    }

    @PostMapping("/updatePrivateVisualModel")
    public RemoteResult updatePrivateVisualModel(@RequestBody ESVisualModel model) {
        return new RemoteResult(visualModelsPeer.updatePrivateVisualModel(model));
    }

    /**
     * 元模型发布审批
     */
    @PostMapping("/approveVisualModel")
    public RemoteResult approveVisualModel(@RequestBody VisualModelsDto dto) {
        return new RemoteResult(visualModelsPeer.approveVisualModel(dto));
    }

    /**
     * 执行工作流审批
     */
    @PostMapping("/completeTask")
    public RemoteResult completeTask(@RequestBody TaskRequest taskRequest) {
        return new RemoteResult(visualModelsPeer.completeTask(taskRequest));
    }

    /**
     * 获取元模型发布历史
     */
    @GetMapping("/getVisualPublishHistory")
    public RemoteResult getVisualPublishHistory(String designVisualModelId) {
        return new RemoteResult(visualModelsPeer.getVisualPublishHistory(designVisualModelId));
    }

    /**
     * 查询元模型历史CI
     * @param visualModeHistoryId
     * @param classId
     * @return
     */
    @GetMapping("/getVisualModelHistoryCi")
    @ModDesc(desc = "查询元模型历史CI", pDesc = "元模型历史id,分类id", pType = Long.class, rDesc = "元模型历史CI", rType = ClassInfoHistory.class)
    public RemoteResult getVisualModelHistoryCi(Long visualModeHistoryId,Long classId){

        Assert.notNull(classId, "分类id不能为空!");
        Assert.notNull(visualModeHistoryId, "元模型历史id不能为空!");
        return new RemoteResult(visualModelsPeer.getVisualModelHistoryCi(visualModeHistoryId,classId));
    }

    /**
     * 查询元模型历史关系信息
     * @param visualModeHistoryId
     * @param rltId
     * @return
     */
    @GetMapping("/getVisualModelHistoryRlt")
    @ModDesc(desc = "查询元模型历史关系信息", pDesc = "元模型历史id,关系分类id", pType = Long.class, rDesc = "元模型历史关系信息", rType = RltInfoHistory.class)
    public RemoteResult getVisualModelHistoryRlt(Long visualModeHistoryId,Long rltId){

        Assert.notNull(rltId, "关系分类id不能为空!");
        Assert.notNull(visualModeHistoryId, "元模型历史id不能为空!");
        return new RemoteResult(visualModelsPeer.getVisualModelHistoryRlt(visualModeHistoryId,rltId));
    }

    /**
     * 删除元模型接口
     * @param dto
     * @return
     */
    @PostMapping("/delPrivateVisualModel")
    public RemoteResult delPrivateVisualModel(@RequestBody VisualModelsDto dto) {
        visualModelsPeer.delPrivateVisualModel(dto.getVisualModelId());
        return new RemoteResult(true);
    }

    @PostMapping("/delDesignVisualModel")
    public RemoteResult delDesignVisualModel(@RequestBody VisualModelsDto dto) {
        visualModelsPeer.delDesignVisualModel(dto.getVisualModelId());
        return new RemoteResult(true);
    }

    /**
     * 集团元模型设置/取消标准元模型
     * @param esVisualModel
     * @return
     */
    @PostMapping("/convertVisualModelStandard")
    @ModDesc(desc = "集团元模型设置/取消标准元模型", pDesc = "集团元模型设置/取消标准元模型Id", rDesc = "转换结果", rType = Long.class)
    public RemoteResult convertVisualModelStandard(@RequestBody ESVisualModel esVisualModel) {
        return new RemoteResult(visualModelsPeer.convertVisualModelStandard(esVisualModel.getId(),esVisualModel.getGroupStandard()));
    }

    @PostMapping("/createVisualFromStandard")
    @ModDesc(desc = "从集团元模型创建本地元模型", pDesc = "集团元模型设置/取消标准元模型Id", rDesc = "保存结果", rType = Long.class)
    public RemoteResult createVisualFromStandard(@RequestBody ESVisualModel esVisualModel) {
        String result;
        try {
            result = visualModelsPeer.createVisualFromStandard(esVisualModel.getId());
        } catch (ClassConflictException exception) {
            return new RemoteResult(true, 510, exception.getMessage());
        }
        return new RemoteResult(result);
    }

    @GetMapping("/sendMsgToOtherDomain")
    public RemoteResult sendMsgToOtherDomain(Long designVisualModelId){
        visualModelsPeer.sendVisualModelChangeToOtherDomains(designVisualModelId);
        return new RemoteResult("");
    }

    /**
     * 获取集团元模型与租户元模型的差异
     * @param visualModelDiffDto
     * @return
     */
    @PostMapping("/getGroupVisualModelDiff")
    public RemoteResult getGroupVisualModelDiff(@RequestBody VisualModelDiffDto visualModelDiffDto){
        VisualModelDiffResultDto groupVisualModelDiff = visualModelsPeer.getGroupVisualModelDiff(visualModelDiffDto);
        return new RemoteResult(groupVisualModelDiff);
    }

    @PostMapping("/syncGroupClassToCurrentDomain")
    public RemoteResult syncGroupClassToCurrentDomain(@RequestBody CcCiClass ccCiClass){
        Long id = ccCiClass.getId();
        Long domainClassId = visualModelsPeer.syncGroupClassToCurrentDomain(id);
        return new RemoteResult(domainClassId);
    }

    @PostMapping("/syncGroupRltToCurrentDomain")
    public RemoteResult syncGroupRltToCurrentDomain(@RequestBody CcCiClass ccCiClass){
        Long rltId = ccCiClass.getId();
        Long domainRltClassId = visualModelsPeer.syncGroupRltToCurrentDomain(rltId);
        return new RemoteResult(domainRltClassId);
    }

    @GetMapping("getGroupStandardVisualModel")
    public RemoteResult getGroupStandardVisualModel(Long visualModelId) {
        ESVisualModel visualModelDiff = visualModelsPeer.getGroupStandardVisualModel(visualModelId);
        return new RemoteResult(visualModelDiff);
    }

    @GetMapping("/getReferenceVisualList")
    public RemoteResult getReferenceVisualList(){
        return new RemoteResult(visualModelsPeer.getReferenceVisualList());
    }

    /**
     * 删除元模型下的sheet页缩略图
     * @param dto
     * @return
     */
    @PostMapping("/delVMThumbnailBySheetId")
    public RemoteResult delVMThumbnailBySheetId(@RequestBody VisualModelsDto dto) {
        visualModelsPeer.delVMThumbnailBySheetId(dto.getVisualModelId(), dto.getSheetId());
        return new RemoteResult(true);
    }

    @PostMapping(value = "/refreshClassIdByVisId")
    @ModDesc(desc = "根据元模型ID刷新json数据", pDesc = "对象分类数据", pType = CcCiClassInfo.class, rDesc = "分类ID", rType = Long.class)
    public void refreshClassIdByVisId(HttpServletRequest request, HttpServletResponse response, @RequestBody Long visId) {
        Map<Long, Long> res = visualModelsPeer.refreshClassIdByVisId(visId);
        ControllerUtils.returnJson(request, response, res);
    }

    /**
     * 获取当前用户操作状态
     * @return
     */
    @PostMapping("/isEmploy")
    public RemoteResult isEmploy(@RequestBody VisualModelsDto dto) {
        Boolean status = visualModelsPeer.isEmploy(dto.getVisualModelId());
        return new RemoteResult(status);
    }

    /**
     * 保存元模型3D全景配置
     * @param modelPanorama3D 元模型3D全景配置
     * @return 保存结果
     */
    @PostMapping("/saveModelPanorama3D")
    @ModDesc(desc = "保存元模型3D全景配置", pDesc = "元模型3D全景配置", rDesc = "保存结果ID", rType = Long.class)
    public RemoteResult saveModelPanorama3D(@RequestBody ModelPanorama3D modelPanorama3D) {
        Long result = visualModelsPeer.saveModelPanorama3D(modelPanorama3D);
        return new RemoteResult(result);
    }

    /**
     * 根据元模型ID查询3D全景配置
     * @param modelId 元模型ID
     * @return 元模型3D全景配置
     */
    @GetMapping("/getModelPanorama3D")
    @ModDesc(desc = "根据元模型ID查询3D全景配置", pDesc = "元模型ID", rDesc = "元模型3D全景配置", rType = ModelPanorama3D.class)
    public RemoteResult getModelPanorama3D(@RequestParam Long modelId) {
        ModelPanorama3D result = visualModelsPeer.queryModelPanorama3DByModelId(modelId);
        return new RemoteResult(result);
    }

}
