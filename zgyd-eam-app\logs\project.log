2025-07-28 09:43:45.467 INFO [main] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-07-28 09:43:46.390 INFO [restartedMain] com.uinnova.product.eam.EamApplication : 开始加载eam-web模块
2025-07-28 09:43:48.084 INFO [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
2025-07-28 09:43:48.085 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-07-28 09:43:48.085 INFO [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
2025-07-28 09:43:48.088 WARN [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
2025-07-28 09:43:48.088 INFO [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
2025-07-28 09:43:48.089 INFO [restartedMain] com.alibaba.nacos.client.logging.NacosLogging : Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.log4j2.Log4J2NacosLoggingAdapter match org.apache.logging.slf4j.Log4jLogger success.
2025-07-28 09:43:48.707 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Starting EamApplication using Java 17.0.4.1 with PID 20396 (D:\workspace\mycode\zgyd\zgyd-eam-app\eam-web\target\classes started by lichong in D:\workspace\mycode\zgyd\zgyd-eam-app)
2025-07-28 09:43:48.727 INFO [restartedMain] com.uinnova.product.eam.EamApplication : The following 1 profile is active: "local"
2025-07-28 09:43:49.586 INFO [restartedMain] org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-28 09:43:49.587 INFO [restartedMain] org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-28 09:43:59.742 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.747 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.752 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.754 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.758 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.761 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.769 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.773 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.775 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.777 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.781 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.787 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.790 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.794 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.798 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${fuxi-feign-server-name:eam-fuxi}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.802 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.804 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.807 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'tarsier-eam-server.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:43:59.821 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean '${flowable-feign-server-name:eam-flowable}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-07-28 09:44:00.362 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'xssCleaner' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=initBeans; factoryMethodName=xssCleaner; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/init/InitBeans.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=net.dreamlu.mica.xss.config.MicaXssConfiguration; factoryMethodName=xssCleaner; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [net/dreamlu/mica/xss/config/MicaXssConfiguration.class]]
2025-07-28 09:44:02.109 INFO [restartedMain] org.springframework.data.repository.config.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 09:44:02.116 INFO [restartedMain] org.springframework.data.repository.config.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 09:44:02.584 INFO [restartedMain] org.springframework.data.repository.config.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 428 ms. Found 0 Redis repository interfaces.
2025-07-28 09:44:02.844 INFO [restartedMain] org.springframework.beans.factory.support.DefaultListableBeanFactory : Overriding bean definition for bean 'org.springframework.transaction.config.internalTransactionAdvisor' with a different definition: replacing [Root bean: class=org.springframework.transaction.interceptor.BeanFactoryTransactionAttributeSourceAdvisor; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration; factoryMethodName=transactionAdvisor; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [org/springframework/transaction/annotation/ProxyTransactionManagementConfiguration.class]]
2025-07-28 09:44:03.580 INFO [restartedMain] com.uino.api.init.LocalRunConfig : 发现spring-boot配置为本地加载
2025-07-28 09:44:03.711 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : 开始注册Filter相关信息
2025-07-28 09:44:03.846 INFO [restartedMain] org.springframework.cloud.context.scope.GenericScope : BeanFactory id=18ebdc3c-2fe1-366b-a522-963c4c0a3558
2025-07-28 09:44:07.297 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.uinnova.product.eam.config.RedisConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 09:44:07.314 INFO [restartedMain] com.uinnova.product.eam.config.RedisConfig : 初始化 -> [Redis CacheErrorHandler]
2025-07-28 09:44:07.317 WARN [restartedMain] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker : Bean 'errorHandler' of type [com.uinnova.product.eam.config.RedisConfig$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [meterRegistryPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 09:44:09.569 INFO [restartedMain] org.springframework.boot.web.embedded.tomcat.TomcatWebServer : Tomcat initialized with port 1515 (http)
2025-07-28 09:44:09.610 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-1515"]
2025-07-28 09:44:09.613 INFO [restartedMain] org.apache.catalina.core.StandardService : Starting service [Tomcat]
2025-07-28 09:44:09.614 INFO [restartedMain] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/11.0.6]
2025-07-28 09:44:09.853 INFO [restartedMain] org.apache.jasper.servlet.TldScanner : At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-07-28 09:44:09.870 INFO [restartedMain] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring embedded WebApplicationContext
2025-07-28 09:44:09.873 INFO [restartedMain] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 20269 ms
2025-07-28 09:44:09.891 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : ResponseFilter注册成功
2025-07-28 09:44:09.893 INFO [restartedMain] com.uino.init.http.ResponseFilter : response请求拦截器注册成功
2025-07-28 09:44:09.918 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : RefererFilter注册成功
2025-07-28 09:44:09.923 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : StaticFilter注册成功
2025-07-28 09:44:09.924 INFO [restartedMain] com.uino.init.http.StaticFilter : 静态资源请求拦截器注册成功
2025-07-28 09:44:09.929 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PermissionFilter注册成功
2025-07-28 09:44:09.935 INFO [restartedMain] com.uino.init.FilterRegisterConfiguration : PluginOperateLogFilter注册成功
2025-07-28 09:44:11.607 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : RedissonClient, type : single
2025-07-28 09:44:11.629 INFO [restartedMain] com.uino.util.cache.config.RedissonConfiguration : Create redissonClient.
2025-07-28 09:44:11.815 INFO [restartedMain] org.redisson.Version : Redisson 3.22.0
2025-07-28 09:44:12.329 INFO [redisson-netty-2-4] org.redisson.connection.pool.MasterPubSubConnectionPool : 1 connections initialized for 192.168.21.245/192.168.21.245:6379
2025-07-28 09:44:12.462 INFO [redisson-netty-2-19] org.redisson.connection.pool.MasterConnectionPool : 24 connections initialized for 192.168.21.245/192.168.21.245:6379
2025-07-28 09:44:22.309 INFO [restartedMain] org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer : LiveReload server is running on port 35729
2025-07-28 09:44:22.413 INFO [restartedMain] org.springframework.cloud.openfeign.FeignClientFactoryBean : For 'eam-flowable' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 09:44:26.285 INFO [restartedMain] com.uinnova.product.eam.init.EamLocalRunConfig$$SpringCGLIB$$0 : 发现spring-boot配置为本地加载
2025-07-28 09:44:35.900 INFO [restartedMain] com.uino.init.ControllerAspect : web请求日志切面注册成功
2025-07-28 09:44:35.959 INFO [restartedMain] com.uino.init.ExceptionController : 异常处理类注册成功
2025-07-28 09:44:35.977 INFO [restartedMain] com.uino.init.InitBeans : 开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串
2025-07-28 09:44:36.002 INFO [restartedMain] com.uino.init.InitBeans : 注册i18-client成功
2025-07-28 09:44:36.004 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  has refresh language data ... 
2025-07-28 09:44:36.005 INFO [restartedMain] com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient :  load refresh language data [114]. 
2025-07-28 09:44:36.124 INFO [restartedMain] com.uino.init.JudgeProcessBeans : 注册校验链
2025-07-28 09:44:36.827 INFO [restartedMain] com.uino.oauth.common.service.UinoLdapAuthenticationProvider : init  ldap user details manager !
2025-07-28 09:44:36.988 INFO [restartedMain] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor : Autowired annotation is not supported on static fields: private static java.lang.String com.uinnova.product.eam.base.diagram.utils.CommonUtil.prefix1
2025-07-28 09:44:36.989 INFO [restartedMain] org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor : Autowired annotation is not supported on static fields: private static java.lang.String com.uinnova.product.eam.base.diagram.utils.CommonUtil.prefix2
2025-07-28 09:44:37.260 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : init SpecifyIndexDao
2025-07-28 09:44:37.264 INFO [restartedMain] com.uino.monitor.tp.common.SpecifyIndexDao : 192.168.21.245:9200;
2025-07-28 09:44:39.203 INFO [restartedMain] org.springframework.validation.beanvalidation.OptionalValidatorFactoryBean : Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
2025-07-28 09:44:39.409 INFO [restartedMain] com.uino.init.InitBeans : 国际化默认语言为【ZHC】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language
2025-07-28 09:44:39.553 INFO [restartedMain] com.uino.init.InitFrameProp : FrameworkProperties注册成功, Local Space http://192.168.21.245/rsm
2025-07-28 09:44:39.922 INFO [restartedMain] org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name uinoUserDetailsManager
2025-07-28 09:44:40.105 WARN [restartedMain] org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry : One of the patterns in [/tenant/queryList, /system-setting#/license, ,/system/init/initFlowData, /eam/workbenchChargeDone/openApi/todoCount, /sys/getLogos, /eam/workbenchChargeDone/changeAction, /eam/workbenchChargeDone/saveOrUpdate, /sync/syncUserDataBatchToEa, /permission/oauth/resource/cleanOnlineUser, /wiki/getTokenByCode, /login/getLoginMethod, /cj/system/diagram/changeFlowByDiagramIds, /planDesign/updatePlanDiagramIsFlow, /redirectAuth, /getTokenByCode, /refreshToken, /cmdb/dataSet/execute, /eam/user/getUserByRoleName, /cmdb/dataSet/realTimeExecute, /websocket/*/*, /eam/oauth/getTokenByLoginInfo, /trial/saas/login/check, /eam/notice/workflow/msg/save, /planDesign/getPlanForFeign, /planDesign/findRenewVersionPlanList, /flowable/getApprovalUser, /flowable/approval/task, /flowable/batchModifyWorkbenchTask, /rsm/**, /ai/api/**, /webjars/css/*.css, /webjars/js/*.js, /swagger-resources, /v2/api-docs, /doc.html] is missing a leading slash. This is discouraged; please include the leading slash in all your request matcher patterns. In future versions of Spring Security, leaving out the leading slash will result in an exception.
2025-07-28 09:44:41.713 INFO [restartedMain] org.springframework.ldap.core.support.AbstractContextSource : Property 'userDn' not set - anonymous context will be used for read-only operations
2025-07-28 09:44:42.556 INFO [restartedMain] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver : Exposing 1 endpoint beneath base path '/actuator'
2025-07-28 09:44:45.216 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Using default implementation for ThreadExecutor
2025-07-28 09:44:45.236 INFO [restartedMain] org.quartz.core.SchedulerSignalerImpl : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 09:44:45.237 INFO [restartedMain] org.quartz.core.QuartzScheduler : Quartz Scheduler v.2.3.2 created.
2025-07-28 09:44:45.238 INFO [restartedMain] org.quartz.simpl.RAMJobStore : RAMJobStore initialized.
2025-07-28 09:44:45.239 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'\n  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.\n  NOT STARTED.\n  Currently in standby mode.\n  Number of jobs executed: 0\n  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.\n  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.\n
2025-07-28 09:44:45.239 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 09:44:45.239 INFO [restartedMain] org.quartz.impl.StdSchedulerFactory : Quartz scheduler version: 2.3.2
2025-07-28 09:44:45.239 INFO [restartedMain] org.quartz.core.QuartzScheduler : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6c68ef6f
2025-07-28 09:44:47.421 INFO [restartedMain] org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler : Starting...
2025-07-28 09:44:47.422 INFO [restartedMain] org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@285de1ed]]
2025-07-28 09:44:47.425 INFO [restartedMain] org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler : Started.
2025-07-28 09:44:47.425 INFO [restartedMain] org.apache.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-1515"]
2025-07-28 09:44:47.454 INFO [restartedMain] org.springframework.boot.web.embedded.tomcat.TomcatWebServer : Tomcat started on port 1515 (http) with context path '/tarsier-eam'
2025-07-28 09:44:47.611 INFO [restartedMain] com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 09:44:47.611 INFO [restartedMain] com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 09:44:49.608 INFO [restartedMain] com.alibaba.nacos.common.ability.AbstractAbilityControlManager : Ready to get current node abilities...
2025-07-28 09:44:49.611 INFO [restartedMain] com.alibaba.nacos.common.ability.AbstractAbilityControlManager : Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-28 09:44:49.612 INFO [restartedMain] com.alibaba.nacos.common.ability.AbstractAbilityControlManager : Initialize current abilities finish...
2025-07-28 09:44:49.612 INFO [restartedMain] com.alibaba.nacos.common.ability.discover.NacosAbilityManagerHolder : [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-28 09:44:49.752 INFO [restartedMain] com.alibaba.cloud.nacos.registry.NacosServiceRegistry : nacos registry, DEFAULT_GROUP eam-fuxi 192.168.205.1:1515 register finished
2025-07-28 09:44:50.073 INFO [restartedMain] org.springframework.scheduling.quartz.SchedulerFactoryBean : Starting Quartz Scheduler now
2025-07-28 09:44:50.073 INFO [restartedMain] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 09:44:51.677 INFO [restartedMain] com.uinnova.product.eam.EamApplication : Started EamApplication in 65.277 seconds (process running for 68.681)
2025-07-28 09:44:51.696 INFO [restartedMain] com.uinnova.product.eam.init.InitPlatFormManager : 初始化平台管理员角色权限开始...
2025-07-28 09:44:51.743 INFO [restartedMain] com.uinnova.product.eam.init.FlowSystemDataInit : 流程体系数据初始化开关关闭
2025-07-28 09:44:51.743 INFO [restartedMain] com.uinnova.product.eam.service.utils.ConvertBusinessKeyUtil : 转换businessKey: false
2025-07-28 09:44:51.764 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-07-28 09:44:51.764 INFO [restartedMain] com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.DiagramServer : DiagramServer start success
2025-07-28 09:44:51.970 INFO [restartedMain] com.uino.service.util.FileUtil : FileUtil Initialization method called
2025-07-28 09:44:51.975 INFO [restartedMain] com.uinnova.product.eam.EamApplication : EAM application start success :)
2025-07-28 09:45:47.282 INFO [MessageBroker-6] org.springframework.web.socket.config.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 10, active threads = 1, queued tasks = 4, completed tasks = 5]
2025-07-28 09:48:27.279 INFO [http-nio-1515-exec-1] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/tarsier-eam] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 09:48:27.279 INFO [http-nio-1515-exec-1] org.springframework.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-07-28 09:48:27.283 INFO [http-nio-1515-exec-1] org.springframework.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-07-28 09:48:27.782 INFO [http-nio-1515-exec-1] com.uino.web.saas.DomainContextFilter : domainContext:{"domainId":6760659161901204,"domainReqType":"FRONT","saas":true}
2025-07-28 09:48:27.965 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 接收到来自【0:0:0:0:0:0:0:1】请求，uri为【/tarsier-eam/bm/visual/model/getGroupVisualModelDiff】,请求标识为【null】
2025-07-28 09:48:27.980 INFO [http-nio-1515-exec-1] com.uino.init.ControllerAspect : 参数【visualModelDiffDto】为【{"domainSheetId":"3Pj4Nd81gdmA9QvTnD2vd","domainVisualModelId":6813671421317415,"groupSheetId":"3-gaLcwO7c_Se7x77yDGF","groupVisualModelId":6788554235300928,"libType":"DESIGN"}】
2025-07-28 10:15:47.283 INFO [MessageBroker-6] org.springframework.web.socket.config.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 4, completed tasks = 186]
2025-07-28 10:45:47.286 INFO [MessageBroker-11] org.springframework.web.socket.config.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 16, active threads = 1, queued tasks = 4, completed tasks = 367]
2025-07-28 10:50:05.024 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-28 10:50:05.024 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Start destroying ThreadPool
2025-07-28 10:50:05.026 WARN [Thread-5] com.alibaba.nacos.common.executor.ThreadPoolManager : [ThreadPoolManager] Destruction of the end
2025-07-28 10:50:05.024 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Start destroying Publisher
2025-07-28 10:50:05.026 WARN [Thread-16] com.alibaba.nacos.common.http.HttpClientBeanHolder : [HttpClientBeanHolder] Destruction of the end
2025-07-28 10:50:05.026 WARN [Thread-14] com.alibaba.nacos.common.notify.NotifyCenter : [NotifyCenter] Destruction of the end
2025-07-28 10:50:05.061 INFO [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 10:50:05.065 INFO [SpringApplicationShutdownHook] org.springframework.boot.web.embedded.tomcat.GracefulShutdown : Commencing graceful shutdown. Waiting for active requests to complete
