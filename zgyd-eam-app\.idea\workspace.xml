<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="250bdbce-652d-4cf0-a75a-fabe7eb3b8fc" name="更改" comment="feat：添加制品设置/取消标准制品功能&#10;tapd：【制品类型设置为标准功能】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132805">
      <change beforePath="$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/EamArtifactSvcImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/EamArtifactSvcImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="zgyd-V1.0-lichong" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="JvmLoggingSettingsStorage">
    <option name="loggerId" value="Lombok Slf4j" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2zqdNNXk1iSEcZO3MlqszPd3yX4" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.eam [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.eam [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.eam [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.eam [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.eam-model [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;找到数据库连接形参&quot;,
    &quot;Notification.DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;zgyd-zuhu-lichong&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/workspace/mycode/zgyd/zgyd-eam-app/eam-project-web/src/test/resources/initdata&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;应用程序.ProjectRunLocal.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.RunLocal.executor&quot;: &quot;Debug&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\workspace\mycode\zgyd\zgyd-eam-app\eam-project-web\src\test\resources\initdata" />
      <recent name="D:\workspace\mycode\zgyd\zgyd-eam-app\eam-project-web\src\test\resources" />
    </key>
  </component>
  <component name="RunManager" selected="应用程序.RunLocal">
    <configuration name="ProjectRunLocal" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.uinnova.product.eam.web.test.ProjectRunLocal" />
      <module name="eam-project-web" />
      <shortenClasspath name="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RunLocal" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.uinnova.product.eam.web.test.RunLocal" />
      <module name="eam-web" />
      <shortenClasspath name="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="zgyd-eam-app" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="EamWorkableApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="eam-workable" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.uinnova.product.eam.workable.EamWorkableApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.EamWorkableApplication" />
      <item itemvalue="应用程序.RunLocal" />
      <item itemvalue="应用程序.ProjectRunLocal" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.RunLocal" />
        <item itemvalue="应用程序.ProjectRunLocal" />
        <item itemvalue="应用程序.RunLocal" />
        <item itemvalue="应用程序.ProjectRunLocal" />
        <item itemvalue="应用程序.RunLocal" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="250bdbce-652d-4cf0-a75a-fabe7eb3b8fc" name="更改" comment="" />
      <created>1752462495564</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752462495564</updated>
      <workItem from="1752462497059" duration="15226000" />
      <workItem from="1752485334021" duration="82000" />
      <workItem from="1752485467515" duration="123000" />
      <workItem from="1752485620490" duration="982000" />
      <workItem from="1752486622408" duration="129000" />
      <workItem from="1752486776419" duration="21368000" />
      <workItem from="1752631324082" duration="62325000" />
      <workItem from="1752819213968" duration="16867000" />
      <workItem from="1753062129569" duration="83036000" />
      <workItem from="1753321337956" duration="25625000" />
      <workItem from="1753407647296" duration="42287000" />
    </task>
    <task id="LOCAL-00001" summary="feat：适配元模型查询角色多租户改造">
      <option name="closed" value="true" />
      <created>1752567329122</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752567329122</updated>
    </task>
    <task id="LOCAL-00002" summary="feat：1、添加集团元模型设置/取消标准元模型接口。2、添加从集团元模型创建本地元模型接口。&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752749825867</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752749825867</updated>
    </task>
    <task id="LOCAL-00003" summary="feat：1、新增参考元模型接口&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752805503522</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752805503522</updated>
    </task>
    <task id="LOCAL-00004" summary="feat：调整基于标准新建接口，支持批量新建&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752808108402</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752808108402</updated>
    </task>
    <task id="LOCAL-00005" summary="feat：调整基于标准新建接口，支持批量新建&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752808352123</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752808352123</updated>
    </task>
    <task id="LOCAL-00006" summary="feat：调整基于标准新建接口，支持批量新建&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752808693444</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752808693444</updated>
    </task>
    <task id="LOCAL-00007" summary="feat：优化参考元模型接口，去除json数据&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752820201358</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752820201358</updated>
    </task>
    <task id="LOCAL-00008" summary="feat：优化参考元模型接口，转换时取消标准元模型标识&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752828873157</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752828873157</updated>
    </task>
    <task id="LOCAL-00009" summary="feat：优化基于标准元模型新建接口&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1752834604351</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752834604351</updated>
    </task>
    <task id="LOCAL-00010" summary="fix：修复元模型同步子分类报错&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753065005230</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753065005230</updated>
    </task>
    <task id="LOCAL-00011" summary="feat：1、添加分类同步时将关联资产中的分类也添加同步和同步前的校验。2、修改分类冲突校验返回值，前端做优化提示&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753077781545</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753077781545</updated>
    </task>
    <task id="LOCAL-00012" summary="fix：元模型发布需要将referModelIds带上&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753083617492</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753083617492</updated>
    </task>
    <task id="LOCAL-00013" summary="fix：修改元模型校验返回，适配前端处理逻辑&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753086957600</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753086957600</updated>
    </task>
    <task id="LOCAL-00014" summary="feat：集团原模型变更，通知其他租户&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753150368432</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753150368432</updated>
    </task>
    <task id="LOCAL-00015" summary="fix：元模型调整creator字段拼写错误问题&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753152406585</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753152406585</updated>
    </task>
    <task id="LOCAL-00016" summary="fix：消息添加tag字段，避免eam/notice/transform接口报错&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753154540224</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753154540224</updated>
    </task>
    <task id="LOCAL-00017" summary="feat：元模型添加是否更新提示字段，发送通知同时更新该字段，前端添加更新提示，发布时将该字段重置成false&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753165617845</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753165617845</updated>
    </task>
    <task id="LOCAL-00018" summary="feat：新增元模型集团变更后与当前组户对比接口&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753254472641</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753254472641</updated>
    </task>
    <task id="LOCAL-00019" summary="feat：元模型集团对比接口添加元模型名称，优化返回值数据&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753256390766</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753256390766</updated>
    </task>
    <task id="LOCAL-00020" summary="fix：修复元模型更新通知过滤失败问题&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753258403373</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753258403373</updated>
    </task>
    <task id="LOCAL-00021" summary="fix：修复元模型更新没有提示bug&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753259242032</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753259242032</updated>
    </task>
    <task id="LOCAL-00022" summary="fix：同步集团的关系信息添加rootId和fromStandard字段标识关系来源&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753262567156</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1753262567156</updated>
    </task>
    <task id="LOCAL-00023" summary="feat：添加同步集团分类/关系至当前租户下的接口&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753324104903</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1753324104903</updated>
    </task>
    <task id="LOCAL-00024" summary="feat：添加跨租户查询标准元模型信息的接口&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753324777954</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1753324777954</updated>
    </task>
    <task id="LOCAL-00025" summary="feat：同步集团ci时添加关联属性类型处理逻辑。&#10;feat：重构ci同步方法，将单个ci同步和批量处理逻辑合并&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753347892138</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1753347892138</updated>
    </task>
    <task id="LOCAL-00026" summary="fix：调整通知文案，调整通知发送过滤逻辑&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753349475816</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1753349475816</updated>
    </task>
    <task id="LOCAL-00027" summary="fix：通知查询元模型添加过滤条件，过滤掉脏数据&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753352435621</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1753352435621</updated>
    </task>
    <task id="LOCAL-00028" summary="fix：处理制品未传filed导致的报错&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753408598426</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1753408598426</updated>
    </task>
    <task id="LOCAL-00029" summary="fix：元模型多租户基本改造完成，去除元模型和制品的org字段&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753409955449</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1753409955449</updated>
    </task>
    <task id="LOCAL-00030" summary="fix：修改租户间分类批量同步使用不校验ci属性方法&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753414467700</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1753414467700</updated>
    </task>
    <task id="LOCAL-00031" summary="fix：查看在用元模型接口添加角色过滤&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753435722036</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1753435722036</updated>
    </task>
    <task id="LOCAL-00032" summary="fix：修复查看在用元模型接口角色过滤无效问题&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753437286861</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1753437286861</updated>
    </task>
    <task id="LOCAL-00033" summary="fix：元模型对比接口添加集团节点信息&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700">
      <option name="closed" value="true" />
      <created>1753667412967</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1753667412967</updated>
    </task>
    <task id="LOCAL-00034" summary="feat：添加制品设置/取消标准制品功能&#10;tapd：【制品类型设置为标准功能】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132805">
      <option name="closed" value="true" />
      <created>1753686150455</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1753686150455</updated>
    </task>
    <option name="localTasksCounter" value="35" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="1f5d6205-ec96-4699-80be-b1dc1f233db3" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="zgyd-zuhu" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="1f5d6205-ec96-4699-80be-b1dc1f233db3">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="zgyd-zuhu-lichong" />
                      </list>
                    </value>
                  </entry>
                  <entry key="user">
                    <value>
                      <list>
                        <option value="*" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/zgyd-zuhu" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="fix：消息添加tag字段，避免eam/notice/transform接口报错&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：元模型添加是否更新提示字段，发送通知同时更新该字段，前端添加更新提示，发布时将该字段重置成false&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：新增元模型集团变更后与当前组户对比接口&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：元模型集团对比接口添加元模型名称，优化返回值数据&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：修复元模型更新通知过滤失败问题&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：修复元模型更新没有提示bug&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="元模型对比代码优化" />
    <MESSAGE value="fix：同步集团的关系信息添加rootId和fromStandard字段标识关系来源&#10;refactor：重构元模型集团租户和当前租户对比方法&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：同步集团的关系信息添加rootId和fromStandard字段标识关系来源&#10;refactor：重构元模型集团租户和当前租户对比方法&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="暂存一下" />
    <MESSAGE value="feat：添加同步集团分类/关系至当前租户下的接口&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：添加跨租户查询标准元模型信息的接口&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="单个ci同步完毕" />
    <MESSAGE value="feat：同步集团ci时添加关联属性类型处理逻辑。&#10;feat：重构ci同步方法，将单个ci同步和批量处理逻辑合并&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：调整通知文案，调整通知发送过滤逻辑&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：通知查询元模型添加过滤条件，过滤掉脏数据&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：处理制品未传filed导致的报错&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：元模型多租户基本改造完成，去除元模型和制品的org字段&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="处理关联资产属性检查" />
    <MESSAGE value="fix：修改租户间分类批量同步使用不校验ci属性方法&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：查看在用元模型接口添加角色过滤&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：修复查看在用元模型接口角色过滤无效问题&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="fix：元模型对比接口添加集团节点信息&#10;【元模型管理改造】&#10;https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132700" />
    <MESSAGE value="feat：添加制品设置/取消标准制品功能&#10;tapd：【制品类型设置为标准功能】&#10;     https://www.tapd.cn/tapd_fe/61756798/story/detail/1161756798001132805" />
    <MESSAGE value="qwen修改" />
    <option name="LAST_COMMIT_MESSAGE" value="qwen修改" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/TrialSaasSvcImpl.java</url>
          <line>53</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/TrialSaasSvcImpl.java</url>
          <line>44</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-web/src/main/java/com/uinnova/product/eam/web/eam/mvc/EamUserMvc.java</url>
          <line>39</line>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/permission/microservice/impl/UserSvc.java</url>
          <line>1818</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/cmdb/microservice/impl/ImageSvc.java</url>
          <line>585</line>
          <option name="timeStamp" value="38" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/cmdb/microservice/impl/ImageSvc.java</url>
          <line>548</line>
          <option name="timeStamp" value="39" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/cmdb/microservice/impl/ImageSvc.java</url>
          <line>562</line>
          <option name="timeStamp" value="41" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-web/1.0.0-SNAPSHOT/uino-eam-micro-web-1.0.0-SNAPSHOT-sources.jar!/com/uino/web/sys/mvc/DictionaryMvc.java</url>
          <line>59</line>
          <option name="timeStamp" value="58" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/EamCIClassSvc.java</url>
          <line>146</line>
          <option name="timeStamp" value="109" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/cmdb/microservice/impl/CIClassSvc.java</url>
          <line>204</line>
          <option name="timeStamp" value="115" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/cmdb/microservice/impl/CIClassSvc.java</url>
          <line>1152</line>
          <option name="timeStamp" value="136" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-web/src/main/java/com/uinnova/product/eam/web/bm/peer/VisualModelsPeer.java</url>
          <line>550</line>
          <option name="timeStamp" value="143" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/eam-service/src/main/java/com/uinnova/product/eam/service/impl/EamArtifactSvcImpl.java</url>
          <line>389</line>
          <option name="timeStamp" value="144" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/cmdb/microservice/impl/CIClassSvc.java</url>
          <line>1146</line>
          <option name="timeStamp" value="145" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/uino/uino-eam-micro-service/1.0.0-SNAPSHOT/uino-eam-micro-service-1.0.0-SNAPSHOT-sources.jar!/com/uino/service/cmdb/microservice/impl/CIClassSvc.java</url>
          <line>1142</line>
          <option name="timeStamp" value="146" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://E:/mavenlib/repository3/com/alibaba/cloud/spring-cloud-starter-alibaba-nacos-discovery/2023.0.3.2/spring-cloud-starter-alibaba-nacos-discovery-2023.0.3.2.jar!/com/alibaba/cloud/nacos/registry/NacosServiceRegistry.class</url>
          <line>49</line>
          <option name="timeStamp" value="151" />
        </line-breakpoint>
        <breakpoint type="java-exception">
          <properties class="java.lang.NullPointerException" package="java.lang" />
          <option name="timeStamp" value="154" />
        </breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
</project>