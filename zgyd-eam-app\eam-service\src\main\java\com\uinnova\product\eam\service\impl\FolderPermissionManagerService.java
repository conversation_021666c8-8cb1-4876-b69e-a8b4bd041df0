package com.uinnova.product.eam.service.impl;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.FolderPermission;
import com.uinnova.product.eam.comm.model.es.FolderPermissionManager;
import com.uinnova.product.eam.model.dto.FolderPermissionManagerVo;
import com.uinnova.product.eam.model.enums.CategoryTypeEnum;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.IFolderPermissionManagerService;
import com.uinnova.product.eam.service.es.EamFolderPermissionManagerDao;
import com.uino.api.client.permission.IRoleApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件夹权限服务层
 *
 * <AUTHOR>
 * @since 2022/6/30 10:36
 */
@Slf4j
@Service
public class FolderPermissionManagerService implements IFolderPermissionManagerService {

    @Autowired
    private EamFolderPermissionManagerDao folderPermissionManagerDao;
    @Autowired
    private EamCategorySvc eamCategorySvc;
    @Autowired
    private IRoleApiSvc roleApiSvc;
    @Autowired
    private IUserApiSvc userApiSvc;

    /**
     *  查询字段 角色ID 和 用户CODE
     */
    private static final String USER_QUERY_ = "userLoginCode.keyword";
    private static final String ROLE_QUERY_ = "roleId";


    @Override
    public void saveFolderPermissions(FolderPermissionManagerVo folderPermissionManagerVo) {
        // 处理角色权限
        if (!CollectionUtils.isEmpty(folderPermissionManagerVo.getRoleIds())) {
            processRolePermissions(folderPermissionManagerVo);
        }

        // 处理用户权限
        if (!CollectionUtils.isEmpty(folderPermissionManagerVo.getUserCodes())) {
            processUserPermissions(folderPermissionManagerVo);
        }
    }

    /**
     * 处理角色权限
     */
    private void processRolePermissions(FolderPermissionManagerVo folderPermissionManagerVo) {
        List<Long> roleIds = folderPermissionManagerVo.getRoleIds();
        Long dirId = folderPermissionManagerVo.getDirId();

        // 删除现有角色权限
        deleteExistingRolePermissions(dirId, roleIds);

        // 获取角色名称映射
        Map<Long, String> roleNameMap = getRoleNameMap(roleIds);

        // 创建权限原型对象
        FolderPermissionManager prototype = createPermissionPrototype(folderPermissionManagerVo);
        EamCategory currentCategory = eamCategorySvc.getById(dirId, LibType.DESIGN);

        // 创建角色权限列表
        List<FolderPermissionManager> folderPermissionManagers = createRolePermissions(
                roleIds, roleNameMap, prototype, currentCategory);

        // 处理子文件夹继承权限
        if (folderPermissionManagerVo.getFolderApplicationScope().getChildFolderAndFile()) {
            addRoleInheritedPermissions(dirId, roleIds, roleNameMap, prototype, folderPermissionManagers);
        }

        saveFolderPermissions(folderPermissionManagers);
    }

    /**
     * 删除现有角色权限
     */
    private void deleteExistingRolePermissions(Long dirId, List<Long> roleIds) {
        BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("dirId", dirId))
                .filter(QueryBuilders.termsQuery(ROLE_QUERY_, roleIds));
        folderPermissionManagerDao.deleteByQuery(deleteQuery, true);
        deleteFolderPermissionManager(dirId, roleIds, null);
    }

    /**
     * 获取角色名称映射
     */
    private Map<Long, String> getRoleNameMap(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new HashMap<>();
        }
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        BoolQueryBuilder roleQuery = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("id", roleIds))
                .filter(QueryBuilders.termQuery("domainId", currentUserInfo.getDomainId()));
        List<SysRole> rolesByQuery = roleApiSvc.getRolesByQuery(roleQuery);
        return rolesByQuery.stream()
                .collect(Collectors.toMap(SysRole::getId, SysRole::getRoleName));
    }

    /**
     * 创建权限原型对象
     */
    private FolderPermissionManager createPermissionPrototype(FolderPermissionManagerVo folderPermissionManagerVo) {
        FolderPermissionManager prototype = new FolderPermissionManager();
        BeanUtils.copyProperties(folderPermissionManagerVo, prototype);
        prototype.setExtendPermission(Boolean.FALSE);
        return prototype;
    }

    /**
     * 创建角色权限列表
     */
    private List<FolderPermissionManager> createRolePermissions(List<Long> roleIds, Map<Long, String> roleNameMap,
                                                               FolderPermissionManager prototype, EamCategory currentCategory) {
        List<FolderPermissionManager> folderPermissionManagers = new ArrayList<>();
        for (Long roleId : roleIds) {
            try {
                FolderPermissionManager permission = (FolderPermissionManager) prototype.clone();
                permission.setRoleId(roleId);
                String roleName = roleNameMap.get(roleId);
                if (BinaryUtils.isEmpty(roleName)) {
                    log.info("角色名称不存在，roleId: {}", roleId);
                    continue;
                }
                permission.setRoleName(roleName);
                permission.setId(ESUtil.getUUID());
                this.setFolderPermissionIfNeed(currentCategory, permission);
                folderPermissionManagers.add(permission);
            } catch (CloneNotSupportedException e) {
                log.error("角色权限对象复制失败，roleId: {}", roleId, e);
            }
        }
        return folderPermissionManagers;
    }

    /**
     * 添加角色继承权限
     */
    private void addRoleInheritedPermissions(Long dirId, List<Long> roleIds, Map<Long, String> roleNameMap,
                                           FolderPermissionManager prototype, List<FolderPermissionManager> folderPermissionManagers) {
        List<EamCategory> childrenDirList = eamCategorySvc.findAllChildrenListNoAuth(dirId);
        if (CollectionUtils.isEmpty(childrenDirList)) {
            return;
        }

        Set<Long> childDirIds = childrenDirList.stream()
                .map(EamCategory::getId)
                .collect(Collectors.toSet());

        // 查询已设置权限的子文件夹，避免重复设置
        HashBasedTable<Long, Long, FolderPermissionManager> existingTable = getExistingRolePermissions(childDirIds, roleIds);

        // 为子文件夹创建继承权限
        roleIds.forEach(roleId -> {
            childrenDirList.forEach(category -> {
                if (existingTable.get(roleId, category.getId()) == null) {
                    try {
                        FolderPermissionManager inheritedPermission = (FolderPermissionManager) prototype.clone();
                        inheritedPermission.setRoleId(roleId);
                        inheritedPermission.setRoleName(roleNameMap.get(roleId));
                        inheritedPermission.setId(ESUtil.getUUID());
                        inheritedPermission.setDirId(category.getId());
                        inheritedPermission.setExtendPermission(Boolean.TRUE);
                        this.setFolderPermissionIfNeed(category, inheritedPermission);
                        folderPermissionManagers.add(inheritedPermission);
                    } catch (CloneNotSupportedException e) {
                        log.error("子文件夹继承权限对象复制失败，roleId: {}, dirId: {}", roleId, category.getId(), e);
                    }
                }
            });
        });
    }

    /**
     * 获取已存在的角色权限
     */
    private HashBasedTable<Long, Long, FolderPermissionManager> getExistingRolePermissions(Set<Long> childDirIds, List<Long> roleIds) {
        BoolQueryBuilder existingQuery = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("dirId", childDirIds))
                .filter(QueryBuilders.termsQuery(ROLE_QUERY_, roleIds))
                .filter(QueryBuilders.termQuery("extendPermission", Boolean.FALSE));

        List<FolderPermissionManager> existingPermissions = folderPermissionManagerDao.getListByQueryScroll(existingQuery);
        HashBasedTable<Long, Long, FolderPermissionManager> existingTable = HashBasedTable.create();
        existingPermissions.forEach(permission ->
                existingTable.put(permission.getRoleId(), permission.getDirId(), permission));
        return existingTable;
    }

    /**
     * 处理用户权限
     */
    private void processUserPermissions(FolderPermissionManagerVo folderPermissionManagerVo) {
        List<String> userCodes = folderPermissionManagerVo.getUserCodes();
        Long dirId = folderPermissionManagerVo.getDirId();

        // 删除现有用户权限
        deleteExistingUserPermissions(dirId, userCodes);

        // 获取用户名称映射
        Map<String, String> userNameMap = getUserNameMap(userCodes);

        // 创建用户权限原型对象
        FolderPermissionManager userPrototype = createPermissionPrototype(folderPermissionManagerVo);
        EamCategory currentCategory = eamCategorySvc.getById(dirId, LibType.DESIGN);

        // 创建用户权限列表
        List<FolderPermissionManager> folderPermissionManagers = createUserPermissions(
                userCodes, userNameMap, userPrototype, currentCategory);

        // 处理子文件夹继承权限
        if (folderPermissionManagerVo.getFolderApplicationScope().getChildFolderAndFile()) {
            addUserInheritedPermissions(dirId, userCodes, userNameMap, userPrototype, folderPermissionManagers);
        }

        saveFolderPermissions(folderPermissionManagers);
    }

    /**
     * 删除现有用户权限
     */
    private void deleteExistingUserPermissions(Long dirId, List<String> userCodes) {
        BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("dirId", dirId))
                .filter(QueryBuilders.termsQuery(USER_QUERY_, userCodes));
        folderPermissionManagerDao.deleteByQuery(deleteQuery, true);
        deleteFolderPermissionManager(dirId, null, userCodes);
    }

    /**
     * 获取用户名称映射
     */
    private Map<String, String> getUserNameMap(List<String> userCodes) {
        if (CollectionUtils.isEmpty(userCodes)) {
            return new HashMap<>();
        }
        CSysUser userCdt = new CSysUser();
        userCdt.setLoginCodes(userCodes.toArray(new String[0]));
        List<SysUser> userList = userApiSvc.getSysUserByCdt(userCdt);
        return userList.stream()
                .collect(Collectors.toMap(SysUser::getLoginCode, SysUser::getUserName));
    }

    /**
     * 创建用户权限列表
     */
    private List<FolderPermissionManager> createUserPermissions(List<String> userCodes, Map<String, String> userNameMap,
                                                               FolderPermissionManager userPrototype, EamCategory currentCategory) {
        List<FolderPermissionManager> folderPermissionManagers = new ArrayList<>();
        for (String userCode : userCodes) {
            try {
                FolderPermissionManager permission = (FolderPermissionManager) userPrototype.clone();
                permission.setUserLoginCode(userCode);
                String userName = userNameMap.get(userCode);
                if (BinaryUtils.isEmpty(userName)) {
                    log.info("用户名称不存在，userCode: {}", userCode);
                    continue;
                }
                permission.setUserName(userName);
                permission.setId(ESUtil.getUUID());
                this.setFolderPermissionIfNeed(currentCategory, permission);
                folderPermissionManagers.add(permission);
            } catch (CloneNotSupportedException e) {
                log.error("用户权限对象复制失败，userCode: {}", userCode, e);
            }
        }
        return folderPermissionManagers;
    }

    /**
     * 添加用户继承权限
     */
    private void addUserInheritedPermissions(Long dirId, List<String> userCodes, Map<String, String> userNameMap,
                                           FolderPermissionManager userPrototype, List<FolderPermissionManager> folderPermissionManagers) {
        List<EamCategory> childrenDirList = eamCategorySvc.findAllChildrenListNoAuth(dirId);
        if (CollectionUtils.isEmpty(childrenDirList)) {
            return;
        }

        Set<Long> childDirIds = childrenDirList.stream()
                .map(EamCategory::getId)
                .collect(Collectors.toSet());

        // 查询已设置权限的子文件夹，避免重复设置
        HashBasedTable<String, Long, FolderPermissionManager> existingTable = getExistingUserPermissions(childDirIds, userCodes);

        // 为子文件夹创建用户继承权限
        userCodes.forEach(userCode -> {
            childrenDirList.forEach(category -> {
                if (existingTable.get(userCode, category.getId()) == null) {
                    try {
                        FolderPermissionManager inheritedPermission = (FolderPermissionManager) userPrototype.clone();
                        inheritedPermission.setUserLoginCode(userCode);
                        inheritedPermission.setUserName(userNameMap.get(userCode));
                        inheritedPermission.setId(ESUtil.getUUID());
                        inheritedPermission.setDirId(category.getId());
                        inheritedPermission.setExtendPermission(Boolean.TRUE);
                        this.setFolderPermissionIfNeed(category, inheritedPermission);
                        folderPermissionManagers.add(inheritedPermission);
                    } catch (CloneNotSupportedException e) {
                        log.error("用户子文件夹继承权限对象复制失败，userCode: {}, dirId: {}", userCode, category.getId(), e);
                    }
                }
            });
        });
    }

    /**
     * 获取已存在的用户权限
     */
    private HashBasedTable<String, Long, FolderPermissionManager> getExistingUserPermissions(Set<Long> childDirIds, List<String> userCodes) {
        BoolQueryBuilder existingQuery = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("dirId", childDirIds))
                .filter(QueryBuilders.termsQuery(USER_QUERY_, userCodes))
                .filter(QueryBuilders.termQuery("extendPermission", Boolean.FALSE));

        List<FolderPermissionManager> existingPermissions = folderPermissionManagerDao.getListByQueryScroll(existingQuery);
        HashBasedTable<String, Long, FolderPermissionManager> existingTable = HashBasedTable.create();
        existingPermissions.forEach(permission ->
                existingTable.put(permission.getUserLoginCode(), permission.getDirId(), permission));
        return existingTable;
    }

    @Override
    public void saveFolderPermissions(List<FolderPermissionManager> folderPermissionManagers) {
        int batchSize=5000;
        if(folderPermissionManagers.size()<batchSize){
            folderPermissionManagerDao.saveOrUpdateBatch(folderPermissionManagers);
        }else {
            for (int i = 1; i < Integer.MAX_VALUE; i++) {
                int from = (i - 1) * batchSize;
                if (from >= folderPermissionManagers.size()) {
                    break;
                }
                int to = Math.min((i - 1) * batchSize + batchSize, folderPermissionManagers.size());
                List<FolderPermissionManager> folderPermissionManagersBlock = folderPermissionManagers.subList(from, to);
                folderPermissionManagerDao.saveOrUpdateBatch(folderPermissionManagersBlock);
            }
        }
    }

    @Override
    public List<FolderPermissionManager> getFolderPermissionsByDirId(Long dirId) {
        EamCategory eamCategory = eamCategorySvc.getById(dirId, LibType.DESIGN);
        if (eamCategory == null || eamCategory.getDataStatus() != 1) {
            throw new BinaryException("操作的文件夹已删除,请刷新页面");
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("dirId",dirId));

        List<FolderPermissionManager> retList = new ArrayList<>();
        // 增加判断，如果角色已经删除或名称发生变更，返回的权限列表中应该同步更新
        List<FolderPermissionManager> fpmList = folderPermissionManagerDao.getListByQuery(boolQueryBuilder);
        if (CollectionUtils.isEmpty(fpmList)) {
            return retList;
        }

        // 批量查询出最新的角色信息和名称信息
        List<Long> roleIds = new ArrayList<>();
        List<String> userCodes = new ArrayList<>();
        for(FolderPermissionManager fp : fpmList){
            if (!BinaryUtils.isEmpty(fp.getRoleId())) {
                roleIds.add(fp.getRoleId());
            }
            if (!BinaryUtils.isEmpty(fp.getUserLoginCode())) {
                userCodes.add(fp.getUserLoginCode());
            }
        }
        Map<Long, String> roleNameMap = this.getRoleNameMap(roleIds);
        Map<String, String> userNameMap = this.getUserNameMap(userCodes);

        for (FolderPermissionManager fp : fpmList) {
            if (!BinaryUtils.isEmpty(fp.getRoleId())) {
                if (roleNameMap.containsKey(fp.getRoleId())) {
                    // 如果角色未被删除，则更新角色名称至最新
                    fp.setRoleName(roleNameMap.get(fp.getRoleId()));
                    retList.add(fp);
                } else {
                    // 删除文件夹权限列表中已删除的角色
                    BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
                    deleteQuery.filter(QueryBuilders.termQuery(ROLE_QUERY_, fp.getRoleId()));
                    deleteQuery.filter(QueryBuilders.termQuery("dirId", dirId));
                    folderPermissionManagerDao.deleteByQuery(deleteQuery, true);
                }
            }
            if (!BinaryUtils.isEmpty(fp.getUserLoginCode())) {
                if (userNameMap.containsKey(fp.getUserLoginCode())) {
                    // 如果用户未被删除，则更新用户名称至最新
                    fp.setUserName(userNameMap.get(fp.getUserLoginCode()));
                    retList.add(fp);
                } else {
                    // 删除文件夹权限列表中已删除的用户
                    BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
                    deleteQuery.filter(QueryBuilders.termQuery(USER_QUERY_, fp.getUserLoginCode()));
                    deleteQuery.filter(QueryBuilders.termQuery("dirId", dirId));
                    folderPermissionManagerDao.deleteByQuery(deleteQuery, true);
                }
            }
        }

        retList.sort((fp1, fp2) -> {
            boolean fp1HasRoleId = !BinaryUtils.isEmpty(fp1.getRoleId());
            boolean fp2HasRoleId = !BinaryUtils.isEmpty(fp2.getRoleId());

            // 如果一个有roleId，一个没有，有roleId的排在前面
            if (fp1HasRoleId && !fp2HasRoleId) {
                return -1;
            }
            if (!fp1HasRoleId && fp2HasRoleId) {
                return 1;
            }

            // 如果都有roleId或都没有roleId，则按照ID进行二级排序保证稳定性
            if (fp1HasRoleId && fp2HasRoleId) {
                // 都是角色权限，按roleId排序
                return Long.compare(fp1.getRoleId(), fp2.getRoleId());
            } else {
                // 都是用户权限，按userLoginCode排序
                String code1 = fp1.getUserLoginCode() != null ? fp1.getUserLoginCode() : "";
                String code2 = fp2.getUserLoginCode() != null ? fp2.getUserLoginCode() : "";
                return code1.compareTo(code2);
            }
        });

        return retList;
    }

    @Override
    public List<FolderPermissionManager> getNeedExtendFolderPermissionsByDirId(Long dirId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.filter(QueryBuilders.termQuery("dirId",dirId));
        query.filter(QueryBuilders.termQuery("folderApplicationScope.childFolderAndFile",true));
        return folderPermissionManagerDao.getListByQuery(query);
    }

    @Override
    public void updateFolderPermissionManager(FolderPermissionManager folderPermissionManager) {
        //查询子文件夹
        List<EamCategory> childrenDirList = eamCategorySvc.findAllChildrenListNoAuth(folderPermissionManager.getDirId());

        Long roleId = folderPermissionManager.getRoleId();
        String userLoginCode = folderPermissionManager.getUserLoginCode();

        //查询子文件夹中设置过权限的文件夹
        List<FolderPermissionManager> noExtendFolderPermissionManagerByUser =
                getNoExtendFolderPermissionManagerByUser(childrenDirList, roleId, userLoginCode);

        Set<Long> dirIds = noExtendFolderPermissionManagerByUser
                .stream()
                .map(FolderPermissionManager::getDirId)
                .collect(Collectors.toSet());

        //排除所有不是继承权限的文件夹
        childrenDirList.removeIf(d -> dirIds.contains(d.getId()));
        Set<Long> extendDirIds = childrenDirList.stream().map(EamCategory::getId).collect(Collectors.toSet());
        ArrayList<FolderPermissionManager> folderPermissionManagers = Lists.newArrayList();
        //清除该角色或用户下子文件夹的权限
        BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
        if (!BinaryUtils.isEmpty(userLoginCode)) {
            deleteQuery.filter(QueryBuilders.termQuery(USER_QUERY_, userLoginCode));
        }
        if (!BinaryUtils.isEmpty(roleId)) {
            deleteQuery.filter(QueryBuilders.termQuery(ROLE_QUERY_, roleId));
        }
        deleteQuery.filter(QueryBuilders.termsQuery("dirId", extendDirIds));
        folderPermissionManagerDao.deleteByQuery(deleteQuery, true);

        if (folderPermissionManager.getFolderApplicationScope().getChildFolderAndFile()) {
            //若是继承则批量创建创建权限
            for (EamCategory category : childrenDirList) {
                if (dirIds.contains(category.getId())) {
                    continue;
                }
                try {
                    FolderPermissionManager clone = (FolderPermissionManager) folderPermissionManager.clone();
                    clone.setDirId(category.getId());
                    clone.setId(ESUtil.getUUID());
                    clone.setExtendPermission(Boolean.TRUE);
                    this.setFolderPermissionIfNeed(category, clone);
                    folderPermissionManagers.add(clone);
                } catch (CloneNotSupportedException e) {
                    log.error("复制权限对象失败", e);
                }
            }
        }
        EamCategory category = eamCategorySvc.getById(folderPermissionManager.getDirId(), LibType.DESIGN);
        folderPermissionManager.setExtendPermission(Boolean.FALSE);
        this.setFolderPermissionIfNeed(category, folderPermissionManager);
        folderPermissionManagers.add(folderPermissionManager);
        saveFolderPermissions(folderPermissionManagers);
    }

    @Override
    public void deleteFolderPermissions(Long id) {
        //删除子文件夹权限
        FolderPermissionManager folderPermissionManager = folderPermissionManagerDao.getById(id);
        Long dirId = folderPermissionManager.getDirId();
        List<EamCategory> childrenDirList = eamCategorySvc.findAllChildrenListNoAuth(dirId);
        Set<Long> childIds = childrenDirList.stream().map(EamCategory::getId).collect(Collectors.toSet());
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.filter(QueryBuilders.termsQuery("dirId",childIds));
        if (!BinaryUtils.isEmpty(folderPermissionManager.getRoleId())) {
            query.filter(QueryBuilders.termQuery(ROLE_QUERY_, folderPermissionManager.getRoleId()));
        }
        if (!BinaryUtils.isEmpty(folderPermissionManager.getUserLoginCode())) {
            query.filter(QueryBuilders.termQuery(USER_QUERY_, folderPermissionManager.getUserLoginCode()));
        }
        query.filter(QueryBuilders.termQuery("extendPermission",Boolean.TRUE));
        folderPermissionManagerDao.deleteByQuery(query,true);
        //删除文件夹权限
        folderPermissionManagerDao.deleteById(id);
    }

    @Override
    public void deleteFolderPermissionsIncludeChildByDirId(Long dirId) {
        List<EamCategory> childrenDirList = eamCategorySvc.findAllChildrenListNoAuth(dirId);
        Set<Long> childIds = childrenDirList.stream().map(EamCategory::getId).collect(Collectors.toSet());
        childIds.add(dirId);
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.filter(QueryBuilders.termsQuery("dirId",childIds));
        folderPermissionManagerDao.deleteByQuery(query,true);
    }

    @Override
    public List<FolderPermissionManager> getFolderPermissionsByLoginCodeAndDirId(String loginCode, List<Long> dirIds) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("loginCode.keyword", loginCode));
        boolQueryBuilder.filter(QueryBuilders.termsQuery("dirId", dirIds));
        BoolQueryBuilder readQuery = QueryBuilders.boolQuery();
        readQuery.should(QueryBuilders.termQuery("modelPermission.read", Boolean.TRUE));
        readQuery.should(QueryBuilders.termQuery("folderPermissions.read",Boolean.TRUE));
        boolQueryBuilder.filter(readQuery);
        return folderPermissionManagerDao.getListByQuery(boolQueryBuilder);
    }

    @Override
    public List<FolderPermissionManager> getFolderPermissionsByDirTypeAndUser(String loginCode, int dirType) {
        Set<Long> userRoles = getUserRoles();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery(ROLE_QUERY_, userRoles));
        boolQueryBuilder.filter(QueryBuilders.termQuery("dirType", dirType));
        boolQueryBuilder.filter(QueryBuilders.termsQuery("folderApplicationScope.currentFolder", Boolean.TRUE));
        BoolQueryBuilder readQuery = QueryBuilders.boolQuery();
        readQuery.should(QueryBuilders.termQuery("modelPermission.read", Boolean.TRUE));
        readQuery.should(QueryBuilders.termQuery("folderPermissions.read",Boolean.TRUE));
        boolQueryBuilder.filter(readQuery);
        return folderPermissionManagerDao.getListByQuery(boolQueryBuilder);
    }

    @Override
    public void moveDirChangeFolderPermissions(List<Long> dirIds, Long parentDirId) {
        //清除该用户下子文件夹的权限
        List<EamCategory> childrenDirList = Lists.newArrayList();
        for (Long dirId : dirIds) {
            List<EamCategory> childrenDirList1 = eamCategorySvc.findAllChildrenListNoAuth(dirId);
            childrenDirList.addAll(childrenDirList1);
        }
        if(CollectionUtils.isEmpty(childrenDirList)){
            return;
        }
        Set<Long> allDirIds = childrenDirList.stream().map(EamCategory::getId).collect(Collectors.toSet());
        allDirIds.addAll(dirIds);
        BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
        deleteQuery.filter(QueryBuilders.termsQuery("dirId", allDirIds));
        folderPermissionManagerDao.deleteByQuery(deleteQuery, true);

        //重建文件夹权限
        //1、获取需要集成的文件夹权限
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.filter(QueryBuilders.termQuery("dirId",parentDirId));
        query.filter(QueryBuilders.termQuery("folderApplicationScope.childFolderAndFile",Boolean.TRUE));
        List<FolderPermissionManager> needExtendPermission = folderPermissionManagerDao.getListByQueryScroll(query);
        ArrayList<FolderPermissionManager> folderPermissionManagers = Lists.newArrayList();
        for (FolderPermissionManager folderPermissionManager : needExtendPermission) {
            //根据文件夹新建
            for (EamCategory eamCategory : childrenDirList) {
                try {
                    FolderPermissionManager clone = (FolderPermissionManager)folderPermissionManager.clone();
                    clone.setDirId(eamCategory.getId());
                    clone.setExtendPermission(Boolean.TRUE);
                    clone.setId(ESUtil.getUUID());
                    this.setFolderPermissionIfNeed(eamCategory, clone);
                    folderPermissionManagers.add(clone);
                } catch (CloneNotSupportedException e) {
                    log.error("复制对象出错",e);
                }
            }
        }
        saveFolderPermissions(folderPermissionManagers);
    }

    @Override
    public List<FolderPermissionManager> getFolderPermissionByUserList(List<UserInfo> userList) {
        Set<Long> roleIds = new HashSet<>();
        Set<String> userCodes = new HashSet<>();
        for (UserInfo userInfo : userList) {
            userCodes.add(userInfo.getLoginCode());
            Set<SysRole> roles = userInfo.getRoles();
            for (SysRole role : roles) {
                roleIds.add(role.getId());
            }
        }
        if (CollectionUtils.isEmpty(roleIds) && CollectionUtils.isEmpty(userCodes)) {
            return Lists.newArrayList();
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        BoolQueryBuilder userOrRoleQuery = QueryBuilders.boolQuery();
        if (!CollectionUtils.isEmpty(roleIds)) {
            userOrRoleQuery.should(QueryBuilders.termsQuery(ROLE_QUERY_, roleIds));
        }
        if (!CollectionUtils.isEmpty(userCodes)) {
            userOrRoleQuery.should(QueryBuilders.termsQuery(USER_QUERY_, userCodes));
        }
        query.filter(userOrRoleQuery);
        query.filter(QueryBuilders.termQuery("folderApplicationScope.currentFolder",Boolean.TRUE));
        BoolQueryBuilder readQuery = QueryBuilders.boolQuery();
        readQuery.should(QueryBuilders.termQuery("modelPermission.read", Boolean.TRUE));
        readQuery.should(QueryBuilders.termQuery("folderPermissions.read",Boolean.TRUE));
        query.filter(readQuery);
        return folderPermissionManagerDao.getListByQueryScroll(query);
    }

    @Override
    public List<FolderPermissionManager> getFolderPermissionByDirIds(List<Long> dirIds) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.filter(QueryBuilders.termsQuery("dirId",dirIds));
        return folderPermissionManagerDao.getListByQueryScroll(query);
    }

    /**
     * 获取用户的不是继承权限的文件夹权限
     * @param childrenDirList
     * @param roleId
     * @return
     */
    private List<FolderPermissionManager> getNoExtendFolderPermissionManagerByUser(List<EamCategory> childrenDirList
            , Long roleId, String userCode) {
        Set<Long> dirIds = childrenDirList.stream().map(EamCategory::getId).collect(Collectors.toSet());
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.filter(QueryBuilders.termsQuery("dirId", dirIds));
        if (!BinaryUtils.isEmpty(roleId)) {
            query.filter(QueryBuilders.termQuery(ROLE_QUERY_, roleId));
        }
        if (!BinaryUtils.isEmpty(userCode)) {
            query.filter(QueryBuilders.termQuery(USER_QUERY_, userCode));
        }
        query.filter(QueryBuilders.termQuery("extendPermission", Boolean.FALSE));
        return folderPermissionManagerDao.getListByQueryScroll(query);
    }

    /**
     * 删除父文件夹下用户继承的所有权限
     * @param dirParentId
     * @param roleIds
     */
    private void deleteFolderPermissionManager(Long dirParentId,List<Long> roleIds, List<String> userCodes){
        //查询子文件夹
        List<EamCategory> childrenDirList = eamCategorySvc.findAllChildrenListNoAuth(dirParentId);
        Set<Long> dirIds = childrenDirList.stream().map(EamCategory::getId).collect(Collectors.toSet());
        //清除该用户下子文件夹的权限
        BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
        if (!CollectionUtils.isEmpty(roleIds)) {
            deleteQuery.filter(QueryBuilders.termsQuery(ROLE_QUERY_, roleIds));
        }
        if (!CollectionUtils.isEmpty(userCodes)) {
            deleteQuery.filter(QueryBuilders.termsQuery(USER_QUERY_, userCodes));
        }
        deleteQuery.filter(QueryBuilders.termsQuery("dirId", dirIds));
        deleteQuery.filter(QueryBuilders.termsQuery("extendPermission", Boolean.TRUE));
        folderPermissionManagerDao.deleteByQuery(deleteQuery, true);
    }

    private Set<Long> getUserRoles(){
        UserInfo user = userApiSvc.getUserInfoById(SysUtil.getCurrentUserInfo().getId());
        Set<Long> roleIds = user.getRoles().stream().map(SysRole::getId).collect(Collectors.toSet());
        return roleIds;
    }

    /**
     * 模型无文件夹权限
     * @param category
     * @param folderPermissionManager
     */
    @Override
    public void setFolderPermissionIfNeed(EamCategory category, FolderPermissionManager folderPermissionManager) {
        if (category.getType() == CategoryTypeEnum.MODEL_ROOT.val()) {
            folderPermissionManager.setFolderPermissions(new FolderPermission());
        }
    }

    @Override
    public List<FolderPermissionManager> queryEmptyModelPermission() {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.scriptQuery(new Script("doc['modelPermission.read'].length==0")));
        return folderPermissionManagerDao.getListByQuery(boolQueryBuilder);
    }

    @Override
    public void deleteFolderPermissionsByDirIds(List<Long> dirIds) {
        if (CollectionUtils.isEmpty(dirIds)) {
            return;
        }
        BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
        deleteQuery.filter(QueryBuilders.termsQuery("dirId", dirIds));
        folderPermissionManagerDao.deleteByQuery(deleteQuery, true);
    }

    @Override
    public void deleteFolderPermissionsByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
        deleteQuery.must(QueryBuilders.termsQuery("id", ids));
        folderPermissionManagerDao.deleteByQuery(deleteQuery, true);
    }
}
