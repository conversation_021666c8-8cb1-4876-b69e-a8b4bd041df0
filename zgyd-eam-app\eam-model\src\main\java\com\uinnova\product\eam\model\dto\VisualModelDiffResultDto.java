package com.uinnova.product.eam.model.dto;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025/7/23 11:17
 */
@Data
public class VisualModelDiffResultDto {

    private String groupVisualModelName;

    private String domainVisualModelName;

    private Map<String, List<CcCiClass>> ciChange;

    private Map<String, List<VisualModelRltChangeDto>> rltChange;

    private Map<Long, JSONObject> classJsonMap ;
    private Map<Long, JSONObject> rltJsonMap ;

    public VisualModelDiffResultDto(String groupVisualModelName, String domainVisualModelName
            , Map<String, List<CcCiClass>> ciChange
            , Map<String, List<VisualModelRltChangeDto>> rltChange) {
        this.groupVisualModelName = groupVisualModelName;
        this.domainVisualModelName = domainVisualModelName;
        this.ciChange = ciChange;
        this.rltChange = rltChange;
    }
}
