package com.uinnova.product.eam.model.dto;

import com.uinnova.product.eam.comm.model.es.FolderPermissionManager;
import lombok.Data;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/6/30 13:43
 */
@Data
public class FolderPermissionManagerVo extends FolderPermissionManager {

    /**
     *  角色信息
     */
    private List<Long> roleIds;

    /**
     *  用户信息
     */
    private List<String> userCodes;
}
