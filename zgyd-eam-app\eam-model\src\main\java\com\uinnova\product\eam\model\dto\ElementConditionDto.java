package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> wangchunlei
 * @Date :  2018/10/23
 * @Description : 制品类型的查询条件
 */
@Data
public class ElementConditionDto {

    @Comment("页数")
    private int pageNum =1;

    @Comment("条数")
    private int pageSize=200;

    /**
     * 制品类型名称查询字段
     **/
    private String name;

    /**
     * 制品类型标签查询字段
     **/
    private String tag;

    /**
     * 制品发布状态
     */
    private Integer releaseState;


    /**
     * 制品类型分类 1=业务流程建模 2=业务组件建模 3=需求关联分析 4=IT架构设计 5=其他 6=数据建模-概念实体关系图 7=数据建模-逻辑实体关系图 11=数据建模-系统逻辑实体关系图 12= 数据建模-物理实体关系图
     */
    private Integer typeClassification;

    /**
     * 后置处理器需要的参数
     */
    private Map<String, String> postProcessParam;

    /**
     * 是否需要鉴权
     */
    private boolean needAuth = false;
}
