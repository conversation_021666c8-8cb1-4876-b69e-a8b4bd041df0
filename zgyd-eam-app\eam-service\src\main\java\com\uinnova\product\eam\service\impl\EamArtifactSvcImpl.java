package com.uinnova.product.eam.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.base.util.ExcelUtil;
import com.uinnova.product.eam.comm.model.es.EamArtifact;
import com.uinnova.product.eam.comm.model.es.EamArtifactElement;
import com.uinnova.product.eam.comm.model.es.EamMultiModelHierarchy;
import com.uinnova.product.eam.comm.model.es.EamResource;
import com.uinnova.product.eam.model.*;
import com.uinnova.product.eam.model.cj.domain.PlanArtifact;
import com.uinnova.product.eam.model.diagram.DiagramNodeLinkInfo;
import com.uinnova.product.eam.model.dto.EamHierarchyDto;
import com.uinnova.product.eam.model.dto.ElementConditionDto;
import com.uinnova.product.eam.model.dto.ElementDto;
import com.uinnova.product.eam.model.enums.ArtifactType;
import com.uinnova.product.eam.model.vo.*;
import com.uinnova.product.eam.service.*;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.cj.service.PlanArtifactService;
import com.uinnova.product.eam.service.cj.service.PlanTemplateChapterService;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.EsDiagramSvcV2;
import com.uinnova.product.eam.service.es.EamArtifactDao;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.eam.service.resource.IEamResourceSvc;
import com.uinnova.product.eam.service.utils.VisualModelUtils;
import com.uinnova.product.eam.service.utils.ZipFileUtils;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESVisualModelSvc;
import com.uino.dao.saas.constant.DomainReqType;
import com.uino.dao.saas.context.DomainContext;
import com.uino.dao.saas.context.DomainContextValue;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.sys.microservice.IDictionarySvc;
import com.uino.service.util.FileUtil;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EamArtifactSvcImpl implements IEamArtifactSvc {

    private static final int START_NUM = 2;
    private static final int END_NUM = 100;
    private static final String ARTIFACT_NAME_KEYWORD = "artifactName.keyword";
    private static final String DICT_NAME = "制品类型分类";

    @Value("${http.resource.space}")
    private String httpResourceUrl;
    @Value("${local.resource.space}")
    private String localPath;
    @Resource
    private EamArtifactDao eamArtifactDao;
    @Autowired
    private IDictionarySvc dictSvc;
    @Resource
    private IEamArtifactColumnSvc columnSvc;
    @Resource
    private IEamResourceSvc eamResourceSvc;
    @Resource
    private IEamCIClassApiSvc classApiSvc;
    @Resource
    private IRltClassSvc rltClassSvc;
    @Resource
    private BmConfigSvc bmConfigSvc;
    @Resource
    private IBmHierarchySvc hierarchySvc;
    @Resource
    private IBmMultiModelHierarchySvc modelHierarchySvc;
    @Resource
    private PlanTemplateChapterService planTemplateChapterService;
    @Resource
    private PlanArtifactService planArtifactService;
    @Autowired
    private RsmUtils rsmUtils;
    @Resource
    private EsDiagramSvcV2 diagramApiClient;
    @Resource
    private IUserApiSvc userApiSvc;
    @Autowired
    private ESVisualModelSvc visualModelSvc;

    private static final String JSON_TYPE = "artifact_json";
    
    @Override
    public Long saveOrUpdate(EamArtifactVo cdt) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery(ARTIFACT_NAME_KEYWORD, cdt.getArtifactName().trim()));
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        if (!BinaryUtils.isEmpty(cdt.getId())) {
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("id", cdt.getId()));
        }
        EamArtifact record = eamArtifactDao.selectOne(boolQueryBuilder);
        Assert.isNull(record, "制品类型名称已存在");
        record = EamUtil.copy(cdt, EamArtifact.class);
        if (BinaryUtils.isEmpty(cdt.getId())) {
            record.setId(ESUtil.getUUID());
            //数据状态 1 表示正常
            record.setDataStatus(1);
            //添加的时候默认是未发布状态 0
            record.setReleaseState(0);
            record.setCreateTime(BinaryUtils.getNumberDateTime());
        }
        record.setModifyTime(BinaryUtils.getNumberDateTime());
        String loginCode = "admin";
        try {
            loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        }catch (Exception e){
            log.error("获取用户异常，使用系统用户");
        }
        if(BinaryUtils.isEmpty(cdt.getId())){
            record.setCreator(loginCode);
        }
        record.setModifier(loginCode);
        return eamArtifactDao.saveOrUpdate(record);
    }

    @Override
    public Page<EamArtifactVo> queryArtifactList(ElementConditionDto dto) {
        Map<String, String> artifactDictMap = getArtifactTypeDictMap();
        Integer typeClassification = dto.getTypeClassification();

        // 1. 判断当前用户是否是集团用户
        SysUser currentUser = SysUtil.getCurrentUserInfo();
        boolean isGroupUser = Objects.equals(currentUser.getDomainId(), BaseConst.DEFAULT_DOMAIN_ID);

        List<EamArtifact> finalArtifactList;
        long totalRows = 0;
        int pageNum = dto.getPageNum();
        int pageSize = dto.getPageSize();


        // 2. 根据用户类型执行不同查询逻辑
        if (isGroupUser) {
            // 集团用户，保持原逻辑
            BoolQueryBuilder boolQueryBuilder = buildQueryBuilderFromDto(dto);
            Page<EamArtifact> artifactPage = eamArtifactDao.getSortListByQuery(pageNum, pageSize, boolQueryBuilder, getDefaultSort());
            finalArtifactList = artifactPage.getData();
            totalRows = artifactPage.getTotalRows();
        } else {
            // 非集团用户，需要合并查询
            // a. 查询本租户制品
            BoolQueryBuilder tenantQuery = buildQueryBuilderFromDto(dto);
            List<EamArtifact> tenantArtifacts = eamArtifactDao.getListByQuery(tenantQuery);

            // b. 查询集团标准制品
            DomainContextValue originalContext = DomainContext.getDomainContextValue();
            List<EamArtifact> groupStandardArtifacts = new ArrayList<>();
            try {
                DomainContext.setDomainContextValue(DomainContextValue.builder()
                        .domainId(BaseConst.DEFAULT_DOMAIN_ID)
                        .domainReqType(DomainReqType.MANAGE)
                        .saas(Boolean.TRUE).build());

                ElementConditionDto groupDto = new ElementConditionDto();
                BeanUtils.copyProperties(dto, groupDto);
                groupDto.setReleaseState(1); // 必须是已发布的
                BoolQueryBuilder groupQuery = buildQueryBuilderFromDto(groupDto);
                groupQuery.must(QueryBuilders.termQuery("groupStandard", true));
                
                groupStandardArtifacts = eamArtifactDao.getListByQuery(groupQuery);

            } finally {
                DomainContext.setDomainContextValue(originalContext);
            }

            // c. 合并去重、排序和内存分页
            Map<Long, EamArtifact> combinedMap = new LinkedHashMap<>();
            tenantArtifacts.forEach(artifact -> combinedMap.put(artifact.getId(), artifact));
            groupStandardArtifacts.forEach(artifact -> combinedMap.putIfAbsent(artifact.getId(), artifact));

            List<EamArtifact> combinedList = new ArrayList<>(combinedMap.values());
            combinedList.sort(Comparator.comparing(EamArtifact::getModifyTime).reversed());

            totalRows = combinedList.size();
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, combinedList.size());
            if (fromIndex >= toIndex) {
                finalArtifactList = Collections.emptyList();
            } else {
                finalArtifactList = combinedList.subList(fromIndex, toIndex);
            }
        }
        
        if (BinaryUtils.isEmpty(finalArtifactList)) {
            return new Page<>(pageNum, pageSize, 0, 0, new ArrayList<>());
        }

        // 3. 组装VO并返回
        List<EamArtifactVo> list = assembleArtifactVo(finalArtifactList, artifactDictMap);
        
        // 若前端传业务类型参数，则将该类型数据前置
        if (!BinaryUtils.isEmpty(list) && !BinaryUtils.isEmpty(typeClassification)) {
            list = list.stream().sorted(Comparator.comparing(each -> !each.getTypeClassification().equals(typeClassification))).collect(Collectors.toList());
        }

        int totalPages = (int) Math.ceil((double) totalRows / pageSize);
        return new Page<>(pageNum, pageSize, totalRows, totalPages, list);
    }
    
    private BoolQueryBuilder buildQueryBuilderFromDto(ElementConditionDto dto) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));

        if (!BinaryUtils.isEmpty(dto.getName())) {
            List<SysUser> userLike = userApiSvc.getUserInfoByName(dto.getName().trim());
            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
            if (!CollectionUtils.isEmpty(userLike)) {
                Set<String> nameLists = userLike.stream().map(SysUser::getLoginCode).collect(Collectors.toSet());
                shouldQuery.should(QueryBuilders.termsQuery("creator.keyword", nameLists));
            }
            shouldQuery.should(QueryBuilders.wildcardQuery("creator.keyword", "*" + dto.getName().trim() + "*"));
            shouldQuery.should(QueryBuilders.wildcardQuery(ARTIFACT_NAME_KEYWORD, "*" + dto.getName().trim() + "*"));
            boolQueryBuilder.must(shouldQuery);
        }

        if (!BinaryUtils.isEmpty(dto.getTypeClassification())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("typeClassification", dto.getTypeClassification()));
        }
        if (!BinaryUtils.isEmpty(dto.getReleaseState())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("releaseState", dto.getReleaseState()));
        }
        if (!BinaryUtils.isEmpty(dto.getTag())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("typeTag.keyword", dto.getTag()));
        }
        return boolQueryBuilder;
    }
    
    private List<SortBuilder<?>> getDefaultSort() {
        List<SortBuilder<?>> sorts = new ArrayList<>();
        sorts.add(SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC));
        return sorts;
    }
    
    private List<EamArtifactVo> assembleArtifactVo(List<EamArtifact> artifactList, Map<String, String> artifactDictMap) {
        if (CollectionUtils.isEmpty(artifactList)) {
            return Collections.emptyList();
        }

        List<Long> picIds = artifactList.stream()
                .filter(e -> !CollectionUtils.isEmpty(e.getFileIds()) && e.getFileIds().get(0) != null)
                .map(e -> e.getFileIds().get(0)).collect(Collectors.toList());
        Map<Long, String> picMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(picIds)) {
             List<FileResourceMeta> download = eamResourceSvc.download(picIds);
             if(download != null){
                picMap = download.stream().collect(Collectors.toMap(FileResourceMeta::getId, FileResourceMeta::getResPath, (k1, k2) -> k2));
             }
        }


        List<Long> artifactIds = artifactList.stream().map(EamArtifact::getId).collect(Collectors.toList());
        List<ESDiagram> diagramList = diagramApiClient.queryByArtifactIds(artifactIds);
        Map<String, List<ESDiagram>> diagramMap = diagramList.stream().collect(Collectors.groupingBy(ESDiagram::getViewType));
        
        List<String> loginCodes = artifactList.stream().map(EamArtifact::getCreator).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, SysUser> userMap = this.queryUserMap(loginCodes);

        List<EamArtifactVo> list = new ArrayList<>();
        for (EamArtifact artifact : artifactList) {
            if (BinaryUtils.isEmpty(artifact.getCreator())) {
                artifact.setCreator(artifact.getModifier());
            }
            String dictName = artifactDictMap.get(String.valueOf(artifact.getTypeClassification()));
            List<ESDiagram> diagrams = diagramMap.getOrDefault(String.valueOf(artifact.getId()), Collections.emptyList());
            artifact.setUsageCounter(diagrams.size());

            EamArtifactVo vo = EamUtil.copy(artifact, EamArtifactVo.class);
            SysUser user = userMap.get(artifact.getCreator());
            vo.setCreatorName(user == null ? artifact.getCreator() : user.getUserName());
            vo.setArtifactTypeName(dictName);
            if (!CollectionUtils.isEmpty(artifact.getFileIds()) && artifact.getFileIds().get(0) != null) {
                vo.setResPath(picMap.get(artifact.getFileIds().get(0)));
            }
            list.add(vo);
        }
        return list;
    }

    @Override
    public Map<String, String> getArtifactTypeDictMap() {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ESDictionaryItemSearchBean bean = new ESDictionaryItemSearchBean();
        bean.setDomainId(currentUserInfo.getDomainId());
        bean.setDictName(DICT_NAME);
        List<ESDictionaryItemInfo> dictionaryItems = dictSvc.searchDictItemListByBean(bean);
        if(BinaryUtils.isEmpty(dictionaryItems)){
            // 如果当前租户没有，尝试获取集团的
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
            dictionaryItems = dictSvc.searchDictItemListByBean(bean);
            if(BinaryUtils.isEmpty(dictionaryItems)){
                 throw new BinaryException("未查询到[制品类型分类]字典表数据，请联系管理员");
            }
        }
        return dictionaryItems.stream().collect(Collectors.toMap(each -> each.getAttrs().get("ID"), each -> each.getAttrs().get("名称")));
    }

    @Override
    public List<EamArtifactVo> queryByType(List<Integer> type) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        if (!BinaryUtils.isEmpty(type)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("typeClassification", type));
        }
        List<EamArtifact> artifactList = eamArtifactDao.getListByQuery(boolQueryBuilder);
        if(BinaryUtils.isEmpty(artifactList)){
            return Collections.emptyList();
        }
        return EamUtil.copy(artifactList, EamArtifactVo.class);
    }

    @Override
    public EamArtifactVo queryArtifact(Long artifactId) {
        Map<String, String> artifactDictMap = getArtifactTypeDictMap();
        EamArtifact artifact = this.getArtifactId(artifactId, 1);
        if (artifact == null) {
            return null;
        }
        if (artifact.getIsSupportDragAsset() == null) {
            artifact.setIsSupportDragAsset(Boolean.FALSE);
        }
        EamArtifactVo vo = EamUtil.copy(artifact, EamArtifactVo.class);
        String typeName = artifactDictMap.get(vo.getTypeClassification().toString());
        vo.setArtifactTypeName(typeName);
        if(BinaryUtils.isEmpty(vo.getCreator())){
            vo.setCreator(vo.getModifier());
        }

        List<Long> allFileIds = artifact.getFileIds();
        if(CollectionUtils.isEmpty(allFileIds)){
            return vo;
        }

        List<Long> fileIds = allFileIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fileIds)) {
            return vo;
        }
        vo.setFileId(fileIds.get(0));
        //只有一张图片
        List<FileResourceMeta> download = eamResourceSvc.download(fileIds);
        if (BinaryUtils.isEmpty(download)) {
            //获取第一张默认图
            List<DefaultFileVo> defaultFileVos = defaultImage();
            vo.setResPath(defaultFileVos.get(0).getResPath());
        } else {
            vo.setResPath(download.get(0).getResPath());
        }
        return vo;
    }

    @Override
    public List<EamArtifactVo> queryArtifactByIds(List<Long> artifactIds) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("releaseState", 1));
        boolQueryBuilder.must(QueryBuilders.termsQuery("id", artifactIds));
        List<EamArtifact> eamArtifactList = eamArtifactDao.getListByQuery(boolQueryBuilder);
        if (CollectionUtils.isEmpty(eamArtifactList)) {
            return null;
        }
        ArrayList<EamArtifactVo> result = new ArrayList<>();
        List<DefaultFileVo> defaultImg = defaultImage();
        for (EamArtifact artifact : eamArtifactList) {
            EamArtifactVo vo = EamUtil.copy(artifact, EamArtifactVo.class);
            if (!CollectionUtils.isEmpty(artifact.getFileIds())) {
                List<FileResourceMeta> download = eamResourceSvc.download(artifact.getFileIds());
                //获取第一张默认图
                if (BinaryUtils.isEmpty(download)) {
                    vo.setFileId(defaultImg.get(0).getFileId());
                    vo.setResPath(defaultImg.get(0).getResPath());
                }else{
                    vo.setFileId(download.get(0).getId());
                    vo.setResPath(download.get(0).getResPath());
                }
            }
            result.add(vo);
        }
        return result;
    }

    @Override
    public List<DefaultFileVo> defaultImage() {
        List<DefaultFileVo> result = new ArrayList<>();
        String jsonVal = bmConfigSvc.getConfigType("DEFAULT_IMG");
        if(BinaryUtils.isEmpty(jsonVal)){
            return result;
        }
        List<FileResourceMeta> configList = JSON.parseArray(jsonVal, FileResourceMeta.class);
        List<Long> conf = configList.stream().map(FileResourceMeta::getId).collect(Collectors.toList());
        List<FileResourceMeta> download = eamResourceSvc.download(conf);
        if (!CollectionUtils.isEmpty(download)) {
            for (FileResourceMeta each : download) {
                DefaultFileVo vo = new DefaultFileVo();
                vo.setResPath(each.getResPath());
                vo.setFileId(each.getId());
                if (!BinaryUtils.isEmpty(each.getName())) {
                    vo.setImageName(each.getName().substring(0, each.getName().indexOf(".")));
                }
                vo.setDefaultImg(true);
                vo.setCreateTime(0L);
                result.add(vo);
            }
        }
        result.sort(Comparator.comparing(DefaultFileVo::getImageName));
        return result;
    }


    @Override
    public Long releaseArtifact(Long artifactId, Integer releaseState) {
        EamArtifact artifact = eamArtifactDao.getById(artifactId);
        if (!BinaryUtils.isEmpty(artifact)) {
            artifact.setReleaseState(releaseState);
        } else {
            throw new ServerException("制品信息不存在");
        }
        return eamArtifactDao.saveOrUpdate(artifact);
    }

    @Override
    public Long deleteArtifact(Long artifactId) {
        EamArtifact artifact = eamArtifactDao.getById(artifactId);
        if (!BinaryUtils.isEmpty(artifact)) {
            //数据状态[DATA_STATUS]   0=删除，1=正常
            artifact.setDataStatus(0);
        } else {
            throw new ServerException("制品信息不存在");
        }
        return eamArtifactDao.saveOrUpdate(artifact);
    }

    @Override
    public Long getIdByArtifactName(String artifactName) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery(ARTIFACT_NAME_KEYWORD, artifactName.trim()));
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        EamArtifact record = eamArtifactDao.selectOne(boolQueryBuilder);
        if (BinaryUtils.isEmpty(record)) {
            return null;
        }
        return record.getId();
    }

    @Override
    public Long copyArtifact(Long artifactId) {
        EamArtifact artifact = eamArtifactDao.getById(artifactId);
        if (0 == artifact.getDataStatus()) {
            throw new ServerException("制品不存在");
        }
        artifact.setId(ESUtil.getUUID());
        artifact.setReleaseState(0);
        artifact.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
        artifact.setCreateTime(ESUtil.getNumberDateTime());
        artifact.setModifyTime(ESUtil.getNumberDateTime());
        String artifactName = artifact.getArtifactName();
        //根据制品名称找到相应记录
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        String key = "-副本";
        queryBuilder.must(QueryBuilders.wildcardQuery(ARTIFACT_NAME_KEYWORD, artifactName + "*"))
                .mustNot(QueryBuilders.termQuery(ARTIFACT_NAME_KEYWORD, artifactName))
                .must(QueryBuilders.termQuery("dataStatus", 1));
        List<EamArtifact> listByQuery = eamArtifactDao.getListByQuery(queryBuilder);
        //拿到所有的artifactName
        List<String> names = listByQuery.stream().map(EamArtifact::getArtifactName).collect(Collectors.toList());
        String oldName = artifact.getArtifactName();
        if (!names.contains(artifactName + key)) {
            artifact.setArtifactName(artifactName + key);
        } else {
            Set<Integer> set = new HashSet<>(names.size());
            String prefix = oldName + key;
            Pattern pattern = Pattern.compile("(" + prefix + "\\(\\d+\\))");
            Set<String> regexSet = modelHierarchySvc.getTemplateRegexSet(names, pattern);
            //符合正则表达式的取出来遍历；
            for (String name : regexSet) {
                String s = name.substring(name.lastIndexOf("(") + 1, name.lastIndexOf(")"));
                int num = Integer.parseInt(s);
                set.add(num);
            }
            if (BinaryUtils.isEmpty(set)) {
                artifact.setArtifactName(artifactName + key + "(" + 2 + ")");
            }else{
                for (int i = START_NUM; i < END_NUM; i++) {
                    if (!set.contains(i)) {
                        artifact.setArtifactName(artifactName + key + "(" + i + ")");
                        break;
                    }
                }
            }
        }
        eamArtifactDao.saveOrUpdate(artifact);
        //保存分栏数据，更新制品id
        ElementDto dto = new ElementDto();
        dto.setArtifactId(artifactId);
        List<EamArtifactElementVo> elementVoList = columnSvc.queryAllColumns(dto);
        if (!BinaryUtils.isEmpty(elementVoList)) {
            for (EamArtifactElementVo elementVo : elementVoList) {
                elementVo.setArtifactId(artifact.getId());
                elementVo.setId(ESUtil.getUUID());
                elementVo.setCreateTime(ESUtil.getNumberDateTime());
            }
            List<EamArtifactElement> elements = EamUtil.copy(elementVoList, EamArtifactElement.class);
            columnSvc.saveOrUpdateBatch(elements);
        }
        return null;
    }


    @Override
    public ResponseEntity<byte[]> exportArtifact(Long artifactId) {
        //获取制品基本信息
        EamArtifact artifact = eamArtifactDao.getById(artifactId);
        Assert.notNull(artifact, "制品不存在,请检查");
        artifact.setArtifactName(artifact.getArtifactName().replaceAll("/", "-"));
        List<File> fileList = new ArrayList<>();
        //  默认图也无差别处理，都下载
        List<FileResourceMeta> download = eamResourceSvc.download(artifact.getFileIds());
        if (!CollectionUtils.isEmpty(download)) {
            fileList = download.stream().map(e -> new File(e.getResPath().replace(httpResourceUrl, localPath))).collect(Collectors.toList());
        }
        //查询制品分栏数据
        List<EamArtifactElement> elements = columnSvc.queryByArtifactIdWithoutUrl(artifactId, null);
        //再处理一下url前缀
        for (EamArtifactElement element : elements) {
            List<String> replaceList = element.getElements().stream().map(e -> e.replaceAll(httpResourceUrl, "")).collect(Collectors.toList());
            element.setElements(replaceList);
        }
        ArtifactVo vo = new ArtifactVo(artifact, EamUtil.copy(elements, EamArtifactElementVo.class));
        vo.setIdent(JSON_TYPE);
        //待压缩文件/目录所在的目录
        String path = localPath + File.separator + LocalDate.now() + File.separator;
        File jsonFile = new File(path + artifact.getArtifactName() + ".json");
        try {
            FileUtil.writeFile(jsonFile, JSON.toJSONString(vo).getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        fileList.add(jsonFile);
        //指定压缩完成后zip文件的存储路径
        String namePath = path + artifact.getArtifactName() + ESUtil.getNumberDateTime() + ".zip";
        ZipFileUtils.zipFiles(fileList, namePath);
        File file = new File(namePath);
        ResponseEntity<byte[]> responseEntity = ExcelUtil.returnRes(file);
        //删制品
        FileUtils.deleteQuietly(jsonFile);
        //删压缩包
        FileUtils.deleteQuietly(file);
        return responseEntity;
    }

    @Override
    public Long importArtifact(File file)  {
        Assert.isTrue(ZipFileUtils.isZipFile(file), "导入文件格式不正确");
        String userName;
        try {
            userName= SysUtil.getCurrentUserInfo().getUserName();
        } catch (Exception e) {
            userName = "system";
        }
        //解压zip
        ArtifactVo artifactVo = this.parseArtifactZipFile(file, userName);
        EamArtifactVo artifact = EamUtil.copy(artifactVo.getArtifact(), EamArtifactVo.class);
        Long artifactId = ESUtil.getUUID();
        artifact.setId(artifactId);
        if (CollectionUtils.isEmpty(artifactVo.getElements())) {
            this.saveOrUpdate(artifact);
            return null;
        }
        List<CcCiClassInfo> ciClassList = classApiSvc.getByClassCodes(null, 1L);
        Map<String, CcCiClass> nameMap = new HashMap<>();
        Map<String, CcCiClass> codeMap = new HashMap<>();
        for (CcCiClassInfo each : ciClassList) {
            nameMap.put(each.getCiClass().getClassName(), each.getCiClass());
            codeMap.put(each.getCiClass().getClassCode(), each.getCiClass());
        }
        Map<String, CcCiClass> classMap = new HashMap<>();
        for (EamArtifactElementVo element : artifactVo.getElements()) {
            List<String> newElements = new ArrayList<>();
            element.setArtifactId(artifactId);
            if(ArtifactType.CI_TYPE.val() == element.getType()){
                for (String each : element.getElements()) {
                    JSONObject jsonObject = JSON.parseObject(each);
                    String type = jsonObject.getString("type");
                    if("class".equals(type)){
                        newElements.add(this.refreshClassInfo(JSON.parseObject(each, EamArtifactCiVo.class), nameMap, codeMap));
                    }else{
                        newElements.add(jsonObject.toJSONString());
                    }
                }
            }else if(ArtifactType.ASSET_TYPE.val() == element.getType() || ArtifactType.DISTINGUISH_TYPE.val() == element.getType()){
                // 设置classMap信息
                List<String> elements = element.getElements();
                for (String assetElement : elements) {
                    CcCiClass ciClass = JSON.parseObject(assetElement, CcCiClass.class);
                    classMap.put(ciClass.getClassCode(), ciClass);
                }
                for (EamArtifactCiVo classVo : element.getElementCiObj()) {
                    newElements.add(this.refreshClassInfo(classVo, nameMap, codeMap));
                }
            }else if(ArtifactType.RLT_TYPE.val() == element.getType()){
                newElements = this.refreshRltInfo(element.getElementRltObj(), classMap, 1L);
            }else{
                continue;
            }
            element.setElements(newElements);
        }
        //保存制品分栏信息
        columnSvc.saveOrUpdateBatch(EamUtil.copy(artifactVo.getElements(), EamArtifactElement.class));
        return this.saveOrUpdate(artifact);
    }

    /**
     * 解析制品zip文件,保存图片信息
     * @param file zip文件
     * @param userName 用户名
     * @return 制品实例
     */
    private ArtifactVo parseArtifactZipFile(File file, String userName){
        String jsonStr = null;
        Long resourceId = null;
        //获得输入流，文件为.zip格式；
        try (ZipInputStream in = new ZipInputStream(Files.newInputStream(file.toPath()));
             ZipFile zipFile = new ZipFile(file, StandardCharsets.UTF_8)) {
            ZipEntry zipEntry;
            //循环读取zip中的cvs/txt文件，zip文件名不能包含中文；
            while ((zipEntry = in.getNextEntry()) != null) {
                String fileName = zipEntry.getName();
                try (InputStream inputStream = zipFile.getInputStream(zipEntry)) {
                    if (fileName.contains("json")) {
                        jsonStr = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
                        continue;
                    }
                    //是图片的话，判断下环境中是够存在这张图片，存在 就直接用存在的名字
                    List<EamResource> resources = eamResourceSvc.getByName(fileName);
                    if (!BinaryUtils.isEmpty(resources)) {
                        resourceId = resources.get(0).getId();
                    } else {
                        String resPath = File.separator + LocalDate.now() + File.separator + fileName;
                        File resFile = new File(localPath + resPath);
                        FileUtil.writeFile(resFile, IOUtils.toByteArray(inputStream));
                        rsmUtils.uploadRsmFromFile(resFile);
                        resourceId = eamResourceSvc.saveOrUpdate(fileName, userName, resPath);
                    }
                } catch (Exception e) {
                    throw new BinaryException("导入文件格式不正确");
                }
            }
        } catch (IOException e) {
            throw new BinaryException(e);
        }
        ArtifactVo artifactVo = this.checkJson(jsonStr);
        artifactVo.getArtifact().setFileIds(Collections.singletonList(resourceId));
        return artifactVo;
    }

    /**
     * 校验制品json内容
     * @param json 制品json数据
     * @return 制品实例
     */
    private ArtifactVo checkJson(String json){
        Assert.notNull(json, "文件内容有误，请检查数据");
        ArtifactVo vo;
        try {
            vo = JSON.parseObject(json, ArtifactVo.class);
        } catch (Exception e) {
            throw new ServiceException("文件内容有误，请检查数据");
        }
        Assert.isFalse(!JSON_TYPE.equals(vo.getIdent()), "文件内容有误，请检查数据");
        Assert.notNull(vo.getArtifact().getArtifactName(), "文件内容有误，请检查数据");
        Assert.notNull(vo.getArtifact().getTypeClassification(), "文件内容有误，请检查数据");
        BoolQueryBuilder artifactQuery = new BoolQueryBuilder();
        artifactQuery.must(QueryBuilders.termQuery(ARTIFACT_NAME_KEYWORD, vo.getArtifact().getArtifactName()));
        artifactQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        List<EamArtifact> list = eamArtifactDao.getListByQuery(artifactQuery);
        if (!CollectionUtils.isEmpty(list)) {
            throw new BinaryException("制品已存在");
        }
        return vo;
    }

    /**
     * 刷新分类信息
     */
    private String refreshClassInfo(EamArtifactCiVo classVo, Map<String, CcCiClass> nameMap, Map<String, CcCiClass> codeMap){
        CcCiClass classInfo = nameMap.get(classVo.getName());
        if(classInfo == null){
            classInfo = codeMap.get(classVo.getClassCode());
        }
        Assert.notNull(classInfo, "导入失败: 当前环境缺少【" + classVo.getName() + "】分类");
        classVo.setId(classInfo.getId().toString());
        classVo.setClassCode(classInfo.getClassCode());
        classVo.setName(classInfo.getClassName());
        // imgFullName/viewFullName/viewIcon看情况处理
        classVo.setShape(classInfo.getShape());
        classVo.setIcon(classInfo.getIcon());
        return JSON.toJSONString(classVo);
    }

    /**
     * 刷新关系信息
     */
    private List<String> refreshRltInfo(List<EamArtifactRltVo> elements, Map<String, CcCiClass> classMap, Long domainId){
        //查询当前环境中所有关系
        List<CcCiClassInfo> rltInfos = rltClassSvc.queryAllClasses(domainId);
        Map<String, CcCiClass> rltMap = rltInfos.stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getClassName, each -> each, (k1, k2) -> k1));

        //校验元模型中的关系数据：获取到 制品中type=2栏中所有的 的架构元素的分类的id
        List<String> ciClassCodes = classMap.values().stream().map(CcCiClass::getClassCode).collect(Collectors.toList());
        // 需兼容跨环境导入导出 根据classCode进行查询
        List<CiClassRltVo> classRltList = this.queryVModelRltionByClassCode(ciClassCodes, rltInfos);

        Map<String, ESCIClassInfo> newEnvRltMap = new HashMap<>();
        for (CiClassRltVo vo : classRltList) {
            ESCIClassInfo sourceCiInfo = vo.getSourceCiInfo();
            newEnvRltMap.put(sourceCiInfo.getClassName(), sourceCiInfo);
            ESCIClassInfo targetCiInfo = vo.getTargetCiInfo();
            newEnvRltMap.put(targetCiInfo.getClassName(), targetCiInfo);
        }

        Map<String, CiClassRltVo> modelMap = new HashMap<>();
        for (CiClassRltVo each : classRltList) {
            String key = each.getSourceCiInfo().getClassName() + each.getRltClassInfo().getCiClass().getClassName() + each.getTargetCiInfo().getClassName();
            modelMap.put(key, each);
        }
        //制品中架构元素关系条数 和 新环境中的元模型数据做对比；
        List<String> result = new ArrayList<>();
        for (EamArtifactRltVo rltVo : elements) {
            //关系
            VcCiClassInfo rltClassInfo = rltVo.getRltClassInfo();
            CcCiClass rltClass = rltClassInfo.getCiClass();
            ESCIClassInfo sourceClass = rltVo.getSourceCiInfo();
            ESCIClassInfo targetClass = rltVo.getTargetCiInfo();
            String rltClassName = rltClass.getClassName().trim();
            String key = sourceClass.getClassName() + rltClassName + targetClass.getClassName();
            //判断对象分类是否存在，元模型中的关系是否存在
            if (!rltMap.containsKey(rltClassName) || !modelMap.containsKey(key)) {
                log.error("导入失败:新环境中的关系分类缺失，异常数据【{}】",JSON.toJSONString("关系名称="+rltClassName));
                continue;
            }
            //当前环境中的关系数据
            CcCiClass currClass = rltMap.get(rltClassName);
            //制品中
            rltClass.setId(currClass.getId());
            rltClass.setIcon(currClass.getIcon());
            rltClass.setClassCode(currClass.getClassCode());
            //源端
            CcCiClass currSource = newEnvRltMap.get(sourceClass.getClassName());
            sourceClass.setId(currSource.getId());
            sourceClass.setClassName(currSource.getClassName());
            sourceClass.setIcon(currSource.getIcon());
            sourceClass.setShape(currSource.getShape());
            sourceClass.setClassCode(currSource.getClassCode());
            //目标端
            CcCiClass currTarget = newEnvRltMap.get(targetClass.getClassName());
            targetClass.setId(currTarget.getId());
            targetClass.setClassName(currTarget.getClassName());
            targetClass.setIcon(currTarget.getIcon());
            targetClass.setShape(currTarget.getShape());
            targetClass.setClassCode(currTarget.getClassCode());
            result.add(JSON.toJSONString(rltVo));
        }
        return result;
    }

    @Override
    public List<EamArtifact> queryByConditions(EamArtifact artifact) {
        EamArtifact copy = EamUtil.copy(artifact, EamArtifact.class);
        return eamArtifactDao.getListByCdt(copy);
    }

    @Override
    public String checkBeforeReleaseArtifact(Long artifactId) {
        //查询层级配置是否引用该制品
        List<EamMultiModelHierarchy> modelList = modelHierarchySvc.queryList(null, 1);
        List<EamHierarchyDto> hierarchyLists = hierarchySvc.queryList(null);
        if (!BinaryUtils.isEmpty(modelList) && !BinaryUtils.isEmpty(hierarchyLists)) {
            List<Long> modelIds = modelList.stream().map(EamMultiModelHierarchy::getId).collect(Collectors.toList());
            for (EamHierarchyDto each : hierarchyLists) {
                if(modelIds.contains(each.getModelId()) && artifactId.equals(each.getArtifactId())){
                    return "该制品类型已被模型树引用，请重新检查";
                }
            }
        }
        //交付物模板
        boolean deliverCheck = planTemplateChapterService.checkViewType(artifactId.toString());
        //方案
        PlanArtifact planArtifact = new PlanArtifact();
        planArtifact.setDiagramProductType(artifactId.toString());
        List<PlanArtifact> planArtifactList = planArtifactService.findPlanArtifactList(planArtifact);
        boolean planCheck = CollectionUtils.isEmpty(planArtifactList);
        if (!deliverCheck && !planCheck) {
            return "该制品类型已被交付物模板和方案引用，请重新检查";
        }
        if (!deliverCheck) {
            return "该制品类型已被交付物模板引用，请重新检查";
        }
        if (!planCheck) {
            return "该制品类型已被方案引用，请重新检查";
        }
        return null;
    }

    @Override
    public List<EamArtifact> queryArtifactListByIds(List<Long> artifactIds, Integer dataStatus) {
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termsQuery("id", artifactIds))
                .must(QueryBuilders.termQuery("dataStatus", dataStatus));
        return eamArtifactDao.getListByQuery(query);
    }

    @Override
    public EamArtifact getArtifactId(Long id, Integer dataStatus) {
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termQuery("id", id));
        query.must(QueryBuilders.termQuery("dataStatus", dataStatus));
        return eamArtifactDao.selectOne(query);
    }

    @Override
    public String crushArtifactColumns() {
        //获取所有得制品，取到制品分栏信息，，根据分类id，查到对应的classCode字段，刷进去 之后再批量保存；
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termQuery("dataStatus", 1));
        List<EamArtifact> queryScroll = eamArtifactDao.getListByQueryScroll(query);
        if (BinaryUtils.isEmpty(queryScroll)) {
            return "制品存量数据已更新";
        }
        List<Long> artifactIds = queryScroll.stream().map(EamArtifact::getId).collect(Collectors.toList());
        Map<Long, List<EamArtifactElementVo>> columnsByIdsMap = columnSvc.queryAllColumnsByIds(artifactIds);
        //查询所有分类
        List<CcCiClassInfo> classList = classApiSvc.getByClassCodes(null, null);
        Map<String, CcCiClass> classMap = classList.stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getClassName, e->e, (k1, k2) -> k1));
        //遍历所有制品
        List<EamArtifactElement> batchArtifact = new ArrayList<>();
        for (Map.Entry<Long, List<EamArtifactElementVo>> entry : columnsByIdsMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            //遍历每一个制品
            for (EamArtifactElementVo elementVo : entry.getValue()) {
                List<String> elements = elementVo.getElements();
                if (CollectionUtils.isEmpty(elements) || BinaryUtils.isEmpty(elementVo.getType()) || elementVo.getType() == 3 || elementVo.getType() == 4) {
                    continue;
                }
                //遍历每个制品的每个分栏
                List<String> newEle = new ArrayList<>();
                for (String element : elements) {
                    //这里如果是形状，不能转化成对象，形状存入的字段和架构元素不一致；
                    JSONObject jsonObject = JSON.parseObject(element);
                    if (BinaryUtils.isEmpty(jsonObject.get("name")) || !"class".equals(jsonObject.get("type"))) {
                        //形状
                        newEle.add(element);
                        continue;
                    }
                    EamArtifactVoCrush ciVo = JSON.parseObject(element, EamArtifactVoCrush.class);
                    String className = ciVo.getName();
                    CcCiClass ciClass = classMap.get(className);
                    if (BinaryUtils.isEmpty(ciClass)) {
                        continue;
                    }
                    ciVo.setId(ciClass.getId());
                    ciVo.setClassCode(ciClass.getClassCode());
                    String icon = ciVo.getIcon();
                    ciVo.setIcon(icon.replaceAll(httpResourceUrl, ""));
                    newEle.add(JSON.toJSONString(ciVo));
                }
                elementVo.setElements(newEle);
                EamArtifactElement ele = EamUtil.copy(elementVo, EamArtifactElement.class);
                batchArtifact.add(ele);
            }
        }
        columnSvc.saveOrUpdateBatch(batchArtifact);
        return "制品存量数据已更新";
    }

    @Override
    public String crushArtifactColumnsRelation() {
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termQuery("dataStatus",1));
        List<EamArtifact> queryScroll = eamArtifactDao.getListByQueryScroll(query);
        List<Long> idList = queryScroll.stream().map(EamArtifact::getId).collect(Collectors.toList());
        List<EamArtifactElement> batchArtifact = new ArrayList<>();
        for (Long id : idList) {
            //把type = 3 这栏全部清掉
            ElementDto elementDto = new ElementDto();
            elementDto.setArtifactId(id);
            elementDto.setType(3);
            List<EamArtifactElementVo> elementVos = columnSvc.queryAllColumns(elementDto);
            for (EamArtifactElementVo elementVo : elementVos) {
                elementVo.setElements(new ArrayList<>());
                EamArtifactElement element = EamUtil.copy(elementVo, EamArtifactElement.class);
                batchArtifact.add(element);
            }
        }
        columnSvc.saveOrUpdateBatch(batchArtifact);
        return null;
    }
    private static final String PRESET_QUERY_TYPE = "11";
    @Override
    public List<EamArtifactVo> queryArtifactByClass(List<EamArtifactVo> eamArtifactVoList, Map<String, String> param) {
        List<EamArtifactVo> result = new ArrayList<>();
        if (BinaryUtils.isEmpty(eamArtifactVoList) || BinaryUtils.isEmpty(param) || BinaryUtils.isEmpty(param.get("classId"))
                || param.get("queryType") == null || !param.get("queryType").equals(PRESET_QUERY_TYPE)) {
            return result;
        }
        // 架构资产类型
        List<Long> artifactIds = eamArtifactVoList.stream().map(EamArtifactVo::getId).collect(Collectors.toList());
        Map<Long, List<EamArtifactElement>> elementGroup = columnSvc.queryByIdsAndType(artifactIds, Collections.singletonList(ArtifactType.ASSET_TYPE.val()));
        for (EamArtifactVo artifactVo : eamArtifactVoList) {
            List<EamArtifactElement> elmentList = elementGroup.get(artifactVo.getId());
            if (BinaryUtils.isEmpty(elmentList)) {
                continue;
            }
            for (EamArtifactElement each : elmentList) {
                for (String element : each.getElements()) {
                    JSONObject elementJson = JSON.parseObject(element);
                    String id = elementJson.getString("id");
                    Boolean viewFlag = elementJson.getBoolean("viewFlag");
                    if (viewFlag && param.get("classId").equals(id)) {
                        result.add(artifactVo);
                        break;
                    }
                }
            }
        }
        return result;
    }


    @Override
    public String crushCreator() {
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termQuery("dataStatus",1));
        List<EamArtifact> queryScroll = eamArtifactDao.getListByQueryScroll(query);
        if(BinaryUtils.isEmpty(queryScroll)){
            return null;
        }
        for (EamArtifact artifact : queryScroll) {
            if(!BinaryUtils.isEmpty(artifact.getModifier()) && BinaryUtils.isEmpty(artifact.getCreator())){
                artifact.setCreator(artifact.getModifier());
            }
        }
        eamArtifactDao.saveOrUpdateBatch(queryScroll);
        return "creator字段刷新完成";
    }

    @Override
    public Map<String, Object> queryArtifactInfoById(Long artifactId) {
        Map<String, Object> result = new HashMap<>();
        EamArtifact artifact = eamArtifactDao.getById(artifactId);
        if (artifact == null) {
            throw new BusinessException("制品已被删除，请退出重试!");
        }
        List<Long> fileIds = artifact.getFileIds().stream().filter(Objects::nonNull).collect(Collectors.toList());
        EamArtifactVo vo = new EamArtifactVo();
        BeanUtils.copyProperties(artifact,vo);
        if (!BinaryUtils.isEmpty(fileIds)) {
            Long fileId = fileIds.get(0);
            vo.setFileId(fileId);
            //只有一张图片
            FileResourceMeta fileResourceMeta = eamResourceSvc.download(fileIds).get(0);
            String picUrl = fileResourceMeta.getResPath();

            try {
                URL url = new URL(picUrl);
                URLConnection uc = url.openConnection();
                int code = ((HttpURLConnection) uc).getResponseCode();
                // 判断URL的状态，200表示成功 404 表示找不到
                if(code == 200){
                    vo.setResPath(fileResourceMeta.getResPath());
                }
                if(code == 404){
                    List<DefaultFileVo> defaultFileVos = defaultImage();
                    vo.setResPath(defaultFileVos.get(0).getResPath());
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        if(!BinaryUtils.isEmpty(vo)){
            result.put("artifact",vo);
        }
        ElementDto elementDto = new ElementDto();
        elementDto.setArtifactId(artifactId);
        List<EamArtifactElementVo> elements = columnSvc.queryAllColumns(elementDto);
        if(!BinaryUtils.isEmpty(elements)){
            //处理模板字段，多返回一个视图名称和icon
            List<EamArtifactElementVo> templateEle = elements.stream().filter(each -> each.getType() == 4).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(templateEle)){
                EamArtifactElementVo elementVo = templateEle.get(0);
                List<String> tempString = elementVo.getElements();
                List<ESDiagram> diagrams = columnSvc.queryTemplateByArtifactId(artifactId, 1, 3000, null).getData();
                if(!BinaryUtils.isEmpty(diagrams) && !BinaryUtils.isEmpty(tempString)){
                    List<String> newElements = new ArrayList<>();
                    Map<String, ESDiagram> diagramMap = diagrams.stream().collect(Collectors.toMap(ESDiagram::getDEnergy, each -> each, (k1, k2) -> k1));
                    for (String temp : tempString) {
                        JSONObject jsonObject = JSON.parseObject(temp);
                        String diagramId = jsonObject.get("diagramId").toString();
                        ESDiagram esDiagram = diagramMap.get(diagramId);
                        jsonObject.put("name",esDiagram.getName());
                        jsonObject.put("icon",esDiagram.getIcon1());
                        newElements.add(jsonObject.toJSONString());
                    }
                    elementVo.setElements(newElements);
                }
            }
            result.put("elements",elements);
        }
        return result;
    }

    @Override
    public List<Long> getArtifactClassIds(Long artifactId) {
        List<EamArtifactElementVo> elementVoList = columnSvc.queryByArtifactId(artifactId, Collections.singletonList(ArtifactType.ASSET_TYPE.val()));
        List<EamArtifactCiVo> elements = elementVoList.stream().map(EamArtifactElementVo::getElementCiObj).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> classIdList = new ArrayList<>();
        for (EamArtifactCiVo element : elements) {
            if ("class".equals(element.getType())) {
                classIdList.add(Long.valueOf(element.getId()));
            }
        }
        return classIdList;
    }

    @Override
    public List<DefaultFileVo> getImages() {
        List<DefaultFileVo> result = this.defaultImage();
        //查询上传的图片
        List<FileResourceMeta> uploadFile = eamResourceSvc.queryByType(1);
        List<DefaultFileVo> customList = new ArrayList<>();
        for (FileResourceMeta each : uploadFile) {
            DefaultFileVo vo = new DefaultFileVo();
            vo.setResPath(each.getResPath());
            vo.setFileId(each.getId());
            vo.setCreateTime(each.getCreateTime());
            if (!BinaryUtils.isEmpty(each.getName())) {
                vo.setImageName(each.getName().substring(0, each.getName().indexOf(".")));
            }
            customList.add(vo);
        }
        customList.sort(Comparator.comparing(DefaultFileVo::getCreateTime));
        result.addAll(customList);
        return result;
    }

    private Map<String, SysUser> queryUserMap(Collection<String> codes) {
        CSysUser cSysUser = new CSysUser();
        cSysUser.setLoginCodes(codes.toArray(new String[0]));
        cSysUser.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
        List<SysUser> userList = userApiSvc.getSysUserByCdt(cSysUser);
        Map<String, SysUser> userIdObjMap = new HashMap<>(userList.size());
        for (SysUser user : userList) {
            user.setLoginPasswd(null);
            String icon = user.getIcon();
            if (icon != null && !icon.startsWith(httpResourceUrl)) {
                user.setIcon(httpResourceUrl + icon);
            }
            userIdObjMap.put(user.getLoginCode(), user);
        }
        return userIdObjMap;
    }

    @Override
    public List<CiSimpleInfoVo> getArtifactClass(Long artifactId) {
        List<Long> classIds = columnSvc.getArtifactClassByType(artifactId, Collections.singletonList(ArtifactType.ASSET_TYPE.val()));
        List<CiSimpleInfoVo> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(classIds)){
            return result;
        }
        List<ESCIClassInfo> classInfoList = classApiSvc.selectCiClassByIds(classIds);
        if(!CollectionUtils.isEmpty(classInfoList)){
            for (ESCIClassInfo each : classInfoList) {
                result.add(new CiSimpleInfoVo(each.getId(), each.getClassName()));
            }
        }
        return result;
    }

    @Override
    public List<CcCiClassInfo> getArtifactRlt(Long artifactId, Long classId) {
        List<EamArtifactElementVo> elements = columnSvc.queryByArtifactId(artifactId, Collections.singletonList(ArtifactType.RLT_TYPE.val()));
        if(CollectionUtils.isEmpty(elements)){
            return Collections.emptyList();
        }
        List<EamArtifactRltVo> rltElements = elements.stream().map(EamArtifactElementVo::getElementRltObj).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> rltClassIds = new ArrayList<>();
        for (EamArtifactRltVo each : rltElements) {
            if(each.getSourceCiInfo().getId().equals(classId) || each.getTargetCiInfo().getId().equals(classId)){
                rltClassIds.add(each.getRltClassInfo().getCiClass().getId());
            }
        }
        if(CollectionUtils.isEmpty(rltClassIds)){
            return Collections.emptyList();
        }
        CCcCiClass cdt = new CCcCiClass();
        cdt.setIds(rltClassIds.toArray(new Long[]{}));
        return rltClassSvc.getRltClassByCdt(cdt);
    }

    @Override
    public List<CiSimpleInfoVo> getArtifactClassByRlt(Long classId, Long artifactId, List<Long> rltIds) {
        List<EamArtifactElementVo> elements = columnSvc.queryByArtifactId(artifactId, Collections.singletonList(ArtifactType.RLT_TYPE.val()));
        if(CollectionUtils.isEmpty(elements)){
            return Collections.emptyList();
        }
        List<EamArtifactRltVo> rltElements = elements.stream().map(EamArtifactElementVo::getElementRltObj).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> classIds = new ArrayList<>();
        for (EamArtifactRltVo each : rltElements) {
            if(!rltIds.contains(each.getRltClassInfo().getCiClass().getId())){
                continue;
            }
            if(each.getSourceCiInfo().getId().equals(classId)){
                classIds.add(each.getTargetCiInfo().getId());
            }else if(each.getTargetCiInfo().getId().equals(classId)){
                classIds.add(each.getSourceCiInfo().getId());
            }
        }
        List<CiSimpleInfoVo> result = new ArrayList<>();
        List<ESCIClassInfo> classInfoList = classApiSvc.selectCiClassByIds(classIds);
        if(!CollectionUtils.isEmpty(classInfoList)){
            for (ESCIClassInfo each : classInfoList) {
                result.add(new CiSimpleInfoVo(each.getId(), each.getClassName()));
            }
        }
        return result;
    }

    /**
     *  根据 classCode 获取包含与元模型内的关系信息 sk-rlt-tk
     */
    private List<CiClassRltVo> queryVModelRltionByClassCode(List<String> classCodes, List<CcCiClassInfo> allRltInfo) {
        if(BinaryUtils.isEmpty(classCodes) || BinaryUtils.isEmpty(allRltInfo)){
            return new ArrayList<>();
        }
        //制品的ci分类集合
        List<CcCiClassInfo> classInfoList = classApiSvc.getByClassCodes(classCodes, BaseConst.DEFAULT_DOMAIN_ID);
        if(BinaryUtils.isEmpty(classInfoList)){
            return new ArrayList<>();
        }
        Map<Long, CcCiClassInfo> classMap = new HashMap<>();
        for (CcCiClassInfo ciClassInfo : classInfoList) {
            classMap.put(ciClassInfo.getCiClass().getId(), ciClassInfo);
        }
        Map<Long, CcCiClassInfo> rltClsMap = allRltInfo.stream().collect(Collectors.toMap(ciClass -> ciClass.getCiClass().getId(), each -> each,(key1, key2) -> key2));
        List<ESVisualModel> model = visualModelSvc.getEnableModel(BaseConst.DEFAULT_DOMAIN_ID);
        if(BinaryUtils.isEmpty(model)){
            return new ArrayList<>();
        }
        List<CiClassRltVo> result = new ArrayList<>();
        Set<String> distinctSet = new HashSet<>();
        List<DiagramNodeLinkInfo> rltLinkList = VisualModelUtils.getRltClassIds(model);
        for (DiagramNodeLinkInfo link : rltLinkList) {
            if(distinctSet.contains(link.getLinkKey())){
                continue;
            }
            distinctSet.add(link.getLinkKey());
            //拿到源端信息，目标端信息，关系信息
            CiClassRltVo ciClassRltVo = new CiClassRltVo();
            ESCIClassInfo sourceCi = new ESCIClassInfo();
            CcCiClassInfo sourcceClassInfo = classMap.get(link.getSourceId());
            if(sourcceClassInfo == null){
                continue;
            }
            String sourceIcon = sourcceClassInfo.getCiClass().getIcon();
            if (sourceIcon != null && !sourceIcon.startsWith(httpResourceUrl)) {
                sourceIcon = httpResourceUrl + sourceIcon;
                sourceCi.setIcon(sourceIcon);
            }
            sourceCi.setClassCode(sourcceClassInfo.getCiClass().getClassCode());
            sourceCi.setClassName(sourcceClassInfo.getCiClass().getClassName());
            sourceCi.setId(sourcceClassInfo.getCiClass().getId());
            sourceCi.setShape(sourcceClassInfo.getCiClass().getShape());
            ciClassRltVo.setSourceCiInfo(sourceCi);

            ESCIClassInfo targetCi = new ESCIClassInfo();
            CcCiClassInfo targetClassInfo = classMap.get(link.getTargetId());
            if(targetClassInfo == null){
                continue;
            }
            String targetIcon = targetClassInfo.getCiClass().getIcon();
            if (targetIcon != null && !targetIcon.startsWith(httpResourceUrl)) {
                targetIcon = httpResourceUrl + targetIcon;
                targetCi.setIcon(targetIcon);
            }
            targetCi.setClassCode(targetClassInfo.getCiClass().getClassCode());
            targetCi.setClassName(targetClassInfo.getCiClass().getClassName());
            targetCi.setId(targetClassInfo.getCiClass().getId());
            targetCi.setShape(targetClassInfo.getCiClass().getShape());
            ciClassRltVo.setTargetCiInfo(targetCi);
            ciClassRltVo.setRltClassInfo(rltClsMap.get(link.getLinkId()));
            ciClassRltVo.setViewName(ciClassRltVo.getRltClassInfo().getCiClass().getClassName());
            result.add(ciClassRltVo);
        }
        return result;
    }

    @Override
    public List<EamArtifact> queryByIds(List<Long> artifactIds) {
        TermsQueryBuilder query = QueryBuilders.termsQuery("id", artifactIds);
        return eamArtifactDao.getListByQuery(query);
    }

    @Override
    public Map<String, String> getArtifactClassShapeById(Long productId) {
        Map<String, String> data = new HashMap<>();
        // 获取资产数据配置栏信息
        List<EamArtifactElementVo> ciEleList = columnSvc.queryByArtifactId(productId, Collections.singletonList(1));
        if (CollectionUtils.isEmpty(ciEleList)) {
            return data;
        }
        for (EamArtifactElementVo vo : ciEleList) {
            List<String> elements = vo.getElements();
            if (CollectionUtils.isEmpty(elements)) {
                continue;
            }
            for (String ele : elements) {
                JSONObject eleObj = JSONObject.parseObject(ele);
                String classCode = eleObj.getString("classCode");
                String shape = eleObj.getString("shape");
                if (BinaryUtils.isEmpty(classCode) || BinaryUtils.isEmpty(shape)) {
                    continue;
                }
                data.put(classCode, shape);
            }
        }
        return data;
    }

    @Override
    public Boolean updateGroupStandard(Long artifactId, Boolean groupStandard) {
        // 1. 判断当前用户是否是集团用户
        Long currentDomainId = SysUtil.getCurrentUserInfo().getDomainId();
        if (!Objects.equals(currentDomainId, 1L)) {
            throw new ServerException("只有集团用户才能修改制品的集团标准标识");
        }

        // 2. 使用制品id查询元模型信息
        EamArtifact artifact = eamArtifactDao.getById(artifactId);
        if (artifact == null) {
            throw new ServerException("制品不存在");
        }

        // 3. 判断制品是否是集团的制品
        if (!Objects.equals(artifact.getDomainId(), 1L)) {
            throw new ServerException("只能修改集团的制品");
        }

        // 4. 将groupStandard使用传入的bool值替换后保存至es中
        artifact.setGroupStandard(groupStandard);
        eamArtifactDao.saveOrUpdate(artifact);
        return true;
    }
}
