package com.uino.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.saas.constant.ESDomainQueryHelper;
import com.uino.dao.util.ESUtil;
import com.uino.util.exception.LoginException;
import com.uino.util.sys.SysUtil;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.*;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.GetMappingsRequest;
import org.elasticsearch.client.indices.GetMappingsResponse;
import org.elasticsearch.cluster.metadata.MappingMetadata;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder.FilterFunctionBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms.Bucket;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Max;
import org.elasticsearch.search.aggregations.metrics.MaxAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.collapse.CollapseBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.xcontent.XContentBuilder;
import org.elasticsearch.xcontent.XContentFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

/**
 * ES操作抽象类
 *
 * <AUTHOR>
 */
@Slf4j
@Data
public abstract class AbstractESBaseDao<T, C> {

    private static final Long DEFAULT_DOMAIN_ID = 1L;

    @Resource
    private RestHighLevelClient client;

    @Value("${data.scope:}")
    private String dataScope;

    // es buffer limit. default:500*1024*1024(500M)
    @Value("${bufferLimitBytes:524288000}")
    private String bufferLimitBytes;

    @Value("${init.data.action:false}")
    private boolean canInit;

    /**
     * 获取索引信息
     *
     * @return
     */
    public abstract String getIndex();

    /**
     * 获取完整索引名称
     *
     * @return
     */
    protected final String getFullIndexName() {
        return getDataScope() + getIndex();
    }

    protected Boolean useDomainId() {
        return true;
    }

    /**
     * 获取索引信息
     *
     * @return
     */
    public abstract String getType();

    /**
     * 获取索引信息
     *
     * @return
     */
    protected final String getFullTypeName() {
        return "_doc";
    }

    /**
     * 获取数据作用域
     *
     * @return
     */
    private String getDataScope() {
        return StringUtils.isNotBlank(dataScope) ? dataScope + "_" : "";
    }


    /**
     * 查询条件填充domainId
     * @param query
     * @return
     */
    private QueryBuilder fillDomainId(QueryBuilder query) {
        //索引是否使用domainId
        if (!this.useDomainId()) {
            return query;
        }
        return ESDomainQueryHelper.getQueryBuilder(query);
    }

    /**
     * 查询条件填充domainId
     * @param query
     * @return
     */
    private BoolQueryBuilder fillDomainId(BoolQueryBuilder query) {
        //索引是否使用domainId
        if (!this.useDomainId()) {
            return query;
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(ESDomainQueryHelper.getQueryBuilder(query));
        return queryBuilder;
    }

    private Class<T> clazz;

    /**
     * 是否打印查询日志，某些子类不想展示则复写该方法返回false即可
     *
     * @return
     */
    protected boolean showQueryLog() {
        return true;
    }

    @SuppressWarnings("unchecked")
    public AbstractESBaseDao() {
        Type type = this.getClass().getGenericSuperclass();
        Type[] types = ((ParameterizedType) type).getActualTypeArguments();
        clazz = (Class<T>) types[0];
    }

    public RestHighLevelClient getClient() {
        return this.client;
    }

    /**
     * <b>获取对象--文档属性id值搜索
     *
     * @param id 文档属性id值搜索
     * @return
     */
    public T getById(Long id) {
        JSONObject rs = new JSONObject();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        SearchRequest searchRequest = this.getSearchRequest();
        TermQueryBuilder query = QueryBuilders.termQuery("id", id);
        QueryBuilder queryBuilder = searchQueryWrapper(query);
        sourceBuilder.query(queryBuilder);
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                rs = JSON.parseObject(recordStr);
                return JSON.toJavaObject(rs, clazz);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return null;
    }

    private JSONObject getJSONObjectById(Long id) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        SearchRequest searchRequest = this.getSearchRequest();
        TermQueryBuilder query = QueryBuilders.termQuery("id", id);
        QueryBuilder queryBuilder = searchQueryWrapper(query);
        sourceBuilder.query(queryBuilder);
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                return JSON.parseObject(recordStr);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return null;
    }

    /**
     * <b> 删除对象
     *
     * @param id 唯一标识ID
     * @return 0:失败，1：成功
     */
    public Integer deleteById(Long id) {
        Integer flag = 1;
        DeleteRequest deleteRequest = this.getDeleteRequest(id.toString());
        deleteRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        try {
            DeleteResponse response = getClient().delete(deleteRequest, RequestOptions.DEFAULT);
            if (response.status().equals(RestStatus.CREATED)) {
                flag = 1;
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            flag = 0;
        }
        return flag;
    }

    /**
     * <b> 保存或更新对象
     *
     * @param obj       对象
     * @param isRefresh 是否立即刷新
     * @return 0失败，其它表示成功
     */
    public Long saveOrUpdate(JSONObject obj, boolean isRefresh) {
        return this.saveOrUpdateWithPrimaryKey(obj, isRefresh, "id");
    }

    /**
     * <b> 保存或更新对象
     *
     * @param obj       对象
     * @param isRefresh 是否立即刷新
     * @return 0失败，其它表示成功
     */
    public Long saveOrUpdateWithPrimaryKey(JSONObject obj, boolean isRefresh, String primaryKey) {
        BinaryUtils.checkNull(primaryKey, "主键");
        Long id = 0L;
        fillPreferencesInfo(obj);
        UpdateRequest uRequest = this.getUpdateRequest(obj.get(primaryKey).toString());
        // 新增失败时重连次数，避免版本冲突报错
        uRequest.retryOnConflict(3);
        uRequest.doc(obj);
        if (isRefresh) {
            uRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        }
        uRequest.docAsUpsert(true);
        try {
            getClient().update(uRequest, RequestOptions.DEFAULT);
            id = obj.getLong("id");
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return id;
    }

    /**
     * <b>批量保存或更新
     *
     * @param list 对象组
     * @return
     */
    public Integer saveOrUpdateBatch(JSONArray list, boolean isRefresh) {
        Integer flag = 1;
        BulkRequest bulkRequest = new BulkRequest();
        SysUser currentUser = null;
        try {
            currentUser = SysUtil.getCurrentUserInfo();
        } catch (LoginException e) {
            log.debug("无法获取当前登陆用户，该持久化信息可能会缺失[创建/修改]人信息和domain域信息不对的问题");
        }
        for (int i = 0; (!list.isEmpty() && i < list.size()); i++) {
            JSONObject obj = list.getJSONObject(i);
            fillPreferencesInfo(obj, currentUser);
            UpdateRequest uRequest = this.getUpdateRequest(obj.get("id").toString());
            uRequest.doc(obj);
            uRequest.docAsUpsert(true);
            bulkRequest.add(uRequest);
        }
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        if (isRefresh) {
            bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        }
        try {
            BulkResponse bulkResponse = getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                flag = 0;
                log.error(bulkResponse.buildFailureMessage());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            flag = 0;
            throw new MessageException(e.getMessage());
        }
        return flag;
    }

    /**
     * 同步批量保存或更新
     *
     * @param datas 对象组
     */
    public boolean syncSaveOrUpdateBatch(Map<String, Object> datas) {
        SysUser currentUser = null;
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        boolean flag = true;
        try {
            currentUser = SysUtil.getCurrentUserInfo();
        } catch (LoginException ignored) {
        }

        if (!datas.isEmpty()) {
            for (Entry<String, Object> entry : datas.entrySet()) {
                JSONObject documentObj = JSON.parseObject(JSON.toJSONString(entry.getValue()));
                fillPreferencesInfo(documentObj, currentUser);

                UpdateRequest uRequest = this.getUpdateRequest(entry.getKey());
                uRequest.doc(documentObj);
                uRequest.docAsUpsert(true);
                bulkRequest.add(uRequest);
            }

            try {
                BulkResponse bulkResponse = getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
                if (bulkResponse.hasFailures()) {
                    flag = false;
                    log.error(bulkResponse.buildFailureMessage());
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new MessageException(e.getMessage());
            }
        }

        return flag;
    }

    /**
     * 异步批量保存或更新
     *
     * @param datas 对象组
     */
    public void asyncSaveOrUpdateBatch(Map<String, Object> datas) {
        SysUser currentUser = null;
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));

        try {
            currentUser = SysUtil.getCurrentUserInfo();
        } catch (LoginException ignored) {
        }

        if (!datas.isEmpty()) {
            for (Entry<String, Object> entry : datas.entrySet()) {
                JSONObject documentObj = JSON.parseObject(JSON.toJSONString(entry.getValue()));
                fillPreferencesInfo(documentObj, currentUser);

                UpdateRequest uRequest = this.getUpdateRequest(entry.getKey());
                uRequest.doc(documentObj);
                uRequest.docAsUpsert(true);
                bulkRequest.add(uRequest);
            }

            getClient().bulkAsync(bulkRequest, RequestOptions.DEFAULT, new ActionListener<BulkResponse>() {
                @Override
                public void onResponse(BulkResponse bulkItemResponses) {
                    boolean hasFailures = bulkItemResponses.hasFailures();
                    if (hasFailures) {
                        log.error(">>> bulkAsync save document has failures:{}", bulkItemResponses.buildFailureMessage());
                    }
                }

                @Override
                public void onFailure(Exception e) {
                    log.error(">>> save document error", e);
                }
            });
        }

    }

    /**
     * 批量保存或更新并返回保存详情
     *
     * @param list    对象组
     * @param isAsync 是否异步调用
     * @return
     */
    public Map<String, Object> saveOrUpdateBatchMessage(JSONArray list, Boolean isAsync) {
        return this.saveOrUpdateBatchMessageWithPrimaryKey(list, isAsync, "id");
    }

    /**
     * 批量保存或更新并返回保存详情
     *
     * @param list    对象组
     * @param isAsync 是否异步调用
     * @return
     */
    public Map<String, Object> saveOrUpdateBatchMessageWithPrimaryKey(JSONArray list, Boolean isAsync, String primaryKey) {
        Map<String, Object> retMap = new HashMap<String, Object>();
        if (list != null && list.size() > 0) {
            BulkRequest bulkRequest = new BulkRequest();
            SysUser currentUser = null;
            try {
                currentUser = SysUtil.getCurrentUserInfo();
            } catch (LoginException e) {
                log.debug("无法获取当前登陆用户，该持久化信息可能会缺失[创建/修改]人信息和domain域信息不对的问题");
            }
            for (int i = 0; (!list.isEmpty() && i < list.size()); i++) {
                JSONObject obj = list.getJSONObject(i);
                fillPreferencesInfo(obj, currentUser);
                UpdateRequest uRequest = this.getUpdateRequest(obj.get(primaryKey).toString());
                uRequest.doc(obj);
                uRequest.docAsUpsert(true);
                bulkRequest.add(uRequest);
            }
            bulkRequest.timeout(TimeValue.timeValueMinutes(2));
            // bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
            if (isAsync) {
                getClient().bulkAsync(bulkRequest, RequestOptions.DEFAULT, new ActionListener<BulkResponse>() {

                    int fail = 0;

                    int success = 0;

                    @Override
                    public void onResponse(BulkResponse response) {
                        List<String> errMesList = new ArrayList<String>();
                        for (BulkItemResponse item : response.getItems()) {
                            if (item.isFailed()) {
                                fail++;
                                errMesList.add(item.getFailureMessage());
                            } else {
                                success++;
                            }
                        }
                        if (response.hasFailures()) {
                            log.info(response.buildFailureMessage());
                        }
                        retMap.put("failCount", fail);
                        retMap.put("successCount", success);
                        retMap.put("errMessge", errMesList);
                    }

                    @Override
                    public void onFailure(Exception e) {
                        log.error(e.getMessage(), e);
                    }
                });
            } else {
                int fail = 0;
                bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
                try {
                    BulkResponse bulkResponse = getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
                    List<String> errMesList = new ArrayList<String>();
                    for (BulkItemResponse item : bulkResponse.getItems()) {
                        if (item.isFailed()) {
                            fail++;
                            errMesList.add(item.getFailureMessage());
                        }
                    }
                    if (bulkResponse.hasFailures()) {
                        log.error(bulkResponse.buildFailureMessage());
                    }
                    retMap.put("failCount", fail);
                    retMap.put("errMessge", errMesList);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return retMap;
    }

    /**
     * <b>批量删除对象
     *
     * @param ids
     * @return
     */
    public Integer deleteByIds(Collection<Long> ids) {
        Integer flag = 1;
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        ids.forEach(id -> {
            DeleteRequest delRequest = this.getDeleteRequest(id.toString());
            bulkRequest.add(delRequest);
        });
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        try {
            BulkResponse bulkResponse = getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                flag = 0;
                log.error(bulkResponse.buildFailureMessage());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            flag = 0;
            throw new MessageException(e.getMessage());
        }
        return flag;
    }

    /**
     * <b> 分页获取对象
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @param query    查询条件
     * @return
     */
    public Page<T> getListByQuery(int pageNum, int pageSize, QueryBuilder query) {
        JSONArray rs = new JSONArray();
        SearchRequest searchRequest = this.getSearchRequest();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        sourceBuilder.query(query);
        sourceBuilder.trackTotalHits(true);
        searchRequest.source(sourceBuilder);
        long totalCount = 0L;
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return resultTransPage(pageNum, pageSize, totalCount, rs);
    }

    /**
     * <b> 不分页获取对象
     *
     * @param query 查询条件
     * @return
     */
    public List<T> getListByQuery(QueryBuilder query) {
        Page<T> results = getListByQuery(1, 3000, query);
        //Assert.isTrue(results.getTotalRows() <= 3000, "不分页查询一次最多拉取3000条数据，本次查询已超出");
        // 修改为日志提示避免直接抛出异常导致功能不可用
        if(results.getTotalRows() > 3000){
            log.error("不分页查询一次最多拉取3000条数据，本次查询已超出");
        }
        return results.getData();
    }

    public List<T> selectListByQuery(int pageNum, int pageSize, QueryBuilder query) {
        Page<T> results = getListByQuery(pageNum, pageSize, query);
        return results.getData();
    }

    public T selectOne(QueryBuilder query) {
        Page<T> page = getListByQuery(1, 1, query);
        if (!BinaryUtils.isEmpty(page) && !BinaryUtils.isEmpty(page.getData())) {
            return page.getData().get(0);
        }
        return null;
    }

    /**
     * 获取列表根据查询条件-游标实现
     *
     * @param query
     * @return
     */
    public List<T> getListByQueryScroll(QueryBuilder query) {
        return getSortListByQueryScroll(query,"id",true);
    }

    public List<T> getSortListByQueryScroll(QueryBuilder query,String sortField,boolean isAsc) {
        List<T> returnVals = new LinkedList<>();
        Map<String, Page<T>> pageResult = this.getScrollByQuery(1, 3000, query, sortField, isAsc);
        String scrollId = pageResult.keySet().iterator().next();
        try {
            if (pageResult.get(scrollId).getData() != null && pageResult.get(scrollId).getData().size() > 0) {
                returnVals.addAll(pageResult.get(scrollId).getData());
            }
            if (pageResult.get(scrollId).getTotalRows() > 3000) {
                while (true) {
                    List<T> nextResults = this.getListByScroll(scrollId);
                    if (nextResults != null && nextResults.size() > 0) {
                        returnVals.addAll(nextResults);
                    } else {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
        } finally {
            this.clearScroll(scrollId);
        }
        return returnVals;
    }

    /**
     * <b> 分页获取对象
     *
     * @param pageNum   页码
     * @param pageSize  页大小
     * @param query     查询条件
     * @param sortField 必须是关键字或数字类型字段
     * @param isAsc     是否是升序
     * @return
     */
    public Page<T> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, String sortField, boolean isAsc) {
        SortOrder orderType = isAsc ? SortOrder.ASC : SortOrder.DESC;
        if (sortField == null || sortField.trim().equals("")) {
            sortField = "id";
        }
        // 不知道这里为什么要转驼峰，先注掉了
        // sortField = ESUtil.underlineToCamel(sortField);
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort(sortField).order(orderType));
        return getSortListByQuery(pageNum, pageSize, query, sorts);
    }

    /**
     * <b>排序分页获取对象
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @param query    查询条件
     * @param sorts    排序对象
     * @return
     */
    public Page<T> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts) {
        JSONArray rs = new JSONArray();
        SearchRequest searchRequest = this.getSearchRequest();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        sourceBuilder.query(query);
        for (SortBuilder<?> sort : sorts) {
            if (sort instanceof FieldSortBuilder) {
                FieldSortBuilder sortBuilder = (FieldSortBuilder)sort;
                sortBuilder.unmappedType("integer");
                sourceBuilder.sort(sortBuilder);
            } else {
                sourceBuilder.sort(sort);
            }
        }
        sourceBuilder.trackTotalHits(true);
        searchRequest.source(sourceBuilder);
        if (showQueryLog()) {
            log.debug(searchRequest.toString());
        }
        long totalCount = 0L;
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return resultTransPage(pageNum, pageSize, totalCount, rs);
    }

    /**
     * <b>排序分页获取对象
     *
     * @param pageNum  页码
     * @param pageSize 页大小
     * @param query    查询条件
     * @param sorts    排序对象
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public Page<T> getSortListByHighLightQuery(int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Collection<String> highLightFields) {
        JSONArray rs = new JSONArray();
        SearchRequest searchRequest = this.getSearchRequest();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        sourceBuilder.query(query);
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        if (BinaryUtils.isEmpty(highLightFields)) {
            highlightBuilder.field("attrs.*", 3000);// 高亮的字段
        } else {
            for (String highLightField : highLightFields) {
                highlightBuilder.field(highLightField, 3000);// 高亮的字段
            }
        }
        highlightBuilder.requireFieldMatch(false);// 是否多个字段都高亮
        highlightBuilder.preTags("<b>");// 前缀后缀
        highlightBuilder.postTags("</b>");
        sourceBuilder.highlighter(highlightBuilder);
        for (SortBuilder<?> sort : sorts) {
            if (sort instanceof FieldSortBuilder) {
                FieldSortBuilder sortBuilder = (FieldSortBuilder)sort;
                sortBuilder.unmappedType("integer");
                sourceBuilder.sort(sortBuilder);
            } else {
                sourceBuilder.sort(sort);
            }
        }
        searchRequest.source(sourceBuilder);
        if (showQueryLog()) {
            log.debug(searchRequest.toString());
        }
        long totalCount = 0L;
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                // 解析高亮字段
                Map<String, HighlightField> highlightFields = hit.getHighlightFields();
                Iterator<Entry<String, HighlightField>> it = highlightFields.entrySet().iterator();
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();// 原来的结果
                Object object = sourceAsMap.get("attrs");
                Map attrs = JSON.parseObject(JSON.toJSONString(object), Map.class);
                while (it.hasNext()) {
                    Entry<String, HighlightField> next = it.next();
                    String key = next.getKey();
                    key = key.replace("attrs.", "").replace(".keyword", "");
                    HighlightField highlightField = next.getValue();
                    // 将原来的字段替换为高亮字段即可
                    Text[] fragments = highlightField.fragments();
                    for (Text text : fragments) {
                        attrs.put(key, text.string());
                    }
                }
                sourceAsMap.put("attrs", attrs);
                String recordStr = JSON.toJSONString(sourceAsMap);
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return resultTransPage(pageNum, pageSize, totalCount, rs);
    }

    /**
     * 根据query分页查询-sortQuery其实是匹配上的给打了100分
     *
     * @param pageNum     当前页数start 1
     * @param pageSize    每页条数
     * @param searchQuery 查询条件
     * @param sortQuery   排序匹配条件，匹配上往前
     * @return
     */
    public Page<T> getSortListByQuery(int pageNum, int pageSize, QueryBuilder searchQuery, QueryBuilder sortQuery) {
        JSONArray rs = new JSONArray();
        SearchRequest searchRequest = this.getSearchRequest();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        searchQuery = searchQueryWrapper(searchQuery);
        searchQuery = this.fillDomainId(searchQuery);
        FunctionScoreQueryBuilder query =
                new FunctionScoreQueryBuilder(searchQuery, new FilterFunctionBuilder[]{new FilterFunctionBuilder(sortQuery, ScoreFunctionBuilders.weightFactorFunction(100F))});
        sourceBuilder.query(query);
        // ES 7的TotalHits有最大10000条限制，加此参数可返回精确totalHist值，但对性能会影响
        sourceBuilder.trackTotalHits(true);
        searchRequest.source(sourceBuilder);
        if (showQueryLog()) {
            log.debug(searchRequest.toString());
        }
        long totalCount = 0L;
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return resultTransPage(pageNum, pageSize, totalCount, rs);
    }

    /**
     * 条件查询带排序
     *
     * @param pageNum
     * @param pageSize
     * @param searchCdt
     * @param sortCdt
     * @return
     * @see #getSortListByQuery(int, int, QueryBuilder, QueryBuilder)
     */
    public Page<T> getSortListByCdt(int pageNum, int pageSize, C searchCdt, C sortCdt) {
        QueryBuilder searchQuery = ESUtil.cdtToBuilder(searchCdt);
        QueryBuilder sortQuery = ESUtil.cdtToBuilder(sortCdt);
        return getSortListByQuery(pageNum, pageSize, searchQuery, sortQuery);
    }

    /**
     * group by ${field} 求maxField max
     *
     * @param maxField
     * @param query
     * @return
     */
    public Map<String, BigDecimal> groupByFieldMaxVal(String groupField, String maxField, QueryBuilder query) {
        Map<String, BigDecimal> fieldMaxValMap = new HashMap<>();
        RestHighLevelClient c = getClient();
        SearchRequest searchRequest = this.getSearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        searchSourceBuilder.query(query);
        TermsAggregationBuilder term = AggregationBuilders.terms("field").field(groupField);
        term.size(10000);
        MaxAggregationBuilder maxAggBuilder = AggregationBuilders.max("maxField").field(maxField);
        term.subAggregation(maxAggBuilder);
        searchSourceBuilder.aggregation(term);
        SearchResponse searchResponse;
        try {
            searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations agg = searchResponse.getAggregations();
            Terms levelObject = agg.get("field");
            for (Bucket bucket : levelObject.getBuckets()) {
                String key = bucket.getKeyAsString();
                if (key == null || key.length() <= 0) {
                    key = "";
                }
                Max maxTerm = bucket.getAggregations().get("maxField");
                double val = maxTerm.value();
                if (!(Double.isInfinite(val) || Double.isNaN(val))) {
                    fieldMaxValMap.put(key, new BigDecimal(maxTerm.value()));
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return fieldMaxValMap;
    }

    /**
     * <b>获取统计结果
     *
     * @param field 统计字段
     * @param query 查询条件
     * @return
     */
    public Map<String, Long> groupByCountField(String field, QueryBuilder query) {
        Map<String, Long> map = new HashMap<String, Long>();
        RestHighLevelClient c = getClient();
        SearchRequest searchRequest = this.getSearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 添加查询条件
        //
        // 默认是10
        int size = 1000;
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        searchSourceBuilder.query(query);
        TermsAggregationBuilder term = AggregationBuilders.terms("field").field(field);
        term.size(size);
        searchSourceBuilder.aggregation(term);
        SearchResponse searchResponse;
        try {
            searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations agg = searchResponse.getAggregations();
            Terms levelObject = agg.get("field");
            for (Bucket bucket : levelObject.getBuckets()) {
                String key = bucket.getKeyAsString();
                if (key == null || key.length() <= 0) {
                    key = "";
                }
                map.put(key, bucket.getDocCount());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return map;
    }

    /**
     * <b>获取Mapping配置
     *
     * @param analyzerName 自定义分词器名称
     * @return
     */
    public XContentBuilder getMapping(String analyzerName) {
        try {
            Map<String, Class<?>> initProperties = getAddInitProperties();
            XContentBuilder builder = XContentFactory.jsonBuilder();
            builder.startObject(); // 根对象
            builder.startObject("properties");
            // 固定字段
            builder.field("id", Collections.singletonMap("type", "long"));
            builder.field("createTime", Collections.singletonMap("type", "long"));
            builder.field("modifyTime", Collections.singletonMap("type", "long"));
            // 动态添加字段
            if (initProperties != null && !initProperties.isEmpty()) {
                for (Entry<String, Class<?>> entry : initProperties.entrySet()) {
                    String fieldName = entry.getKey();
                    Class<?> fieldType = entry.getValue();

                    if (fieldType == Long.class) {
                        builder.field(fieldName, Collections.singletonMap("type", "long"));
                    } else if (fieldType == String.class) {
                        String json = """
                        {
                            "analyzer":"my-analyzer",
                            "type":"text",
                            "fields":{
                                "keyword":{"ignore_above":256,"type":"keyword"},
                                "stdkeyword":{"normalizer":"my_normalizer","ignore_above":256,"type":"keyword"}
                            }
                        }
                    """;
                        JSONObject fieldMap = JSON.parseObject(json);
                        builder.field(fieldName, fieldMap.getInnerMap());
                    }
                }
            }
            builder.endObject(); // end "properties"
            // 额外配置
            builder.field("date_detection", false);
            builder.startArray("dynamic_templates");

            builder.startObject().startObject("string_code_stdkeyword")
                    .field("match_mapping_type", "string")
                    .field("match_pattern", "regex")
                    .field("match", ".*?name|.*?code|.*?key|.*?Name|.*?Code|.*?Key")
                    .startObject("mapping")
                    .field("analyzer", analyzerName)
                    .field("type", "text")
                    .startObject("fields")
                    .startObject("keyword").field("ignore_above", 256).field("type", "keyword").endObject()
                    .startObject("stdkeyword").field("ignore_above", 256).field("type", "keyword").field("normalizer", "my_normalizer").endObject()
                    .endObject()
                    .endObject()
                    .endObject().endObject();

            builder.startObject().startObject("strings")
                    .field("match_mapping_type", "string")
                    .startObject("mapping")
                    .field("analyzer", analyzerName)
                    .field("type", "text")
                    .startObject("fields")
                    .startObject("keyword").field("ignore_above", 256).field("type", "keyword").endObject()
                    .endObject()
                    .endObject()
                    .endObject().endObject();

            builder.endArray(); // dynamic_templates
            builder.endObject(); // root object

            return builder;

        } catch (Exception e) {
            log.error("构建 Mapping 出错: " + e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
    }

    /**
     * 额外初始属性索引
     * 默认按照class进行初始化，根据需要重写设置初始化字段
     *
     * @return fieldName:type[Long/String]
     */
    protected Map<String, Class<?>> addInitProperties() {
        Map<String, Class<?>> classMap = new HashMap<String, Class<?>>(32);
        Class<T> tempClazz = clazz;
        //JSONObject对象不进行初始化字段
        if (tempClazz.getName().contains("JSONObject")) {
            return null;
        }
        while (true) {
            Field[] fields = tempClazz.getDeclaredFields();
            if (!BinaryUtils.isEmpty(fields)) {
                for (Field field : fields) {
                    //排除基础字段，防止重复创建问题
                    if (field.getName().equals("id") || field.getName().equals("createTime") || field.getName().equals("modifyTime")) {
                        continue;
                    }
                    //添加初始索引仅支持Long/String
                    if (field.getType().getName().equals("java.lang.String") || field.getType().getName().equals("java.lang.Long")) {
                        if (classMap.containsKey(field.getName())) {
                            continue;
                        }
                        classMap.put(field.getName(), field.getType());
                    }
                }
            }
            //初始化继承父类的属性字段
            if (tempClazz.getSuperclass().getName().equals("java.lang.Object")) {
                break;
            } else {
                tempClazz = (Class<T>) tempClazz.getSuperclass();
            }
        }
        return classMap;
    }

    private Map<String, Class<?>> getAddInitProperties() {
        Map<String, Class<?>> res = addInitProperties();
        if (res != null) {
            res.values().forEach(val -> Assert.isTrue(val == Long.class || val == String.class, "添加初始索引仅支持Long/String"));
        }
        return res;
    }

    /**
     * <b>获取Setting配置
     *
     * @param analyzerName
     * @return
     */
    public Settings getSetting(String analyzerName) {
        //默认一个Shards
        Settings setting = getSetting(analyzerName, 1);
        return setting;
    }

    /**
     * <b>获取Setting配置
     *
     * @param analyzerName
     * @return
     */
    public Settings getSetting(String analyzerName, int number_of_shards) {
        return Settings.builder()
                .put("number_of_shards", number_of_shards)
                .put("number_of_replicas", 1)
                .put("index.max_result_window", 100000)
                // analysis 配置开始
                .put("analysis.normalizer.my_normalizer.type", "custom")
                .putList("analysis.normalizer.my_normalizer.filter", "lowercase")
                .put("analysis.analyzer." + analyzerName + ".type", "custom")
                .put("analysis.analyzer." + analyzerName + ".tokenizer", "standard")
                .putList("analysis.analyzer." + analyzerName + ".filter", "lowercase", "reverse")
                // 如果你用 edge_ngram tokenizer，可以额外配置如下
                // .put("analysis.tokenizer.edge_ngram_tokenizer.type", "edge_ngram")
                // .put("analysis.tokenizer.edge_ngram_tokenizer.min_gram", 1)
                // .put("analysis.tokenizer.edge_ngram_tokenizer.max_gram", 20)
                // .putList("analysis.tokenizer.edge_ngram_tokenizer.token_chars", "letter", "digit")
                .build();
    }

    /**
     * <b>按条件删除
     *
     * @param query     查询条件
     * @param isRefresh 是否立即刷新
     * @return
     */
    public Integer deleteByQuery(QueryBuilder query, boolean isRefresh) {
        RestClient lowClient = getClient().getLowLevelClient();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        query = this.fillDomainId(query);
        sourceBuilder.query(query);
        HttpEntity entity = new NStringEntity(sourceBuilder.toString(), ContentType.APPLICATION_JSON);
        try {
            String url = "/" + getFullIndexName() + "/_delete_by_query?conflicts=proceed&refresh=" + isRefresh;
            Request request = new Request("POST", url);
            request.setEntity(entity);
            Response res = lowClient.performRequest(request);
            if (res.getStatusLine().getStatusCode() != 200) {
                return 0;
            }
            return 1;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
    }

    /**
     * <b>查询更新
     *
     * @param query     查询条件
     * @param scriptStr 查询的脚本,格式：ctx._source.文档字段=值 simple: 多个属性更新：
     *                  "ctx._source.attrs['应用负责人ID']='cgjabc';ctx._source.attrs['b']+=1" 子文档： ctx._source.attrs['应用负责人ID']='abc'
     *                  文档： ctx._source.name='abc'
     * @param isRefresh 是否立即刷新
     * @return
     */
    public boolean updateByQuery(QueryBuilder query, String scriptStr, boolean isRefresh) {
        return updateByQuery(query, scriptStr, null, isRefresh);
    }


    public boolean updateByQuery(QueryBuilder query, final String scriptStr, boolean isRefresh, Map<String, Object> params) {
        RestHighLevelClient client = getClient();
        //参数为索引名，可以不指定，可以一个，可以多个
        UpdateByQueryRequest request = this.getUpdateByQueryRequest();
        // 更新时版本冲突
        request.setConflicts("proceed");
        // 设置查询条件，第一个参数是字段名，第二个参数是字段的值
        query = updateQueryWrapper(query);
        query = this.fillDomainId(query);
        request.setQuery(query);
        request.setRefresh(isRefresh);
        // 更新最大文档数
//        request.setSize(10);
        // 批次大小
        request.setBatchSize(3000);
//		request.setPipeline("my_pipeline");
        Script script = new Script(ScriptType.INLINE, "painless", scriptStr, params);
        request.setScript(script);
        // 并行
        request.setSlices(2);
        // 使用滚动参数来控制“搜索上下文”存活的时间
        request.setScroll(TimeValue.timeValueMinutes(10));
        // 如果提供路由，则将路由复制到滚动查询，将流程限制为匹配该路由值的切分
//		request.setRouting("=cat");

        // 可选参数
        // 超时
        request.setTimeout(TimeValue.timeValueMinutes(2));
        // 刷新索引
        //request.setRefresh(true);
        try {
            BulkByScrollResponse response = client.updateByQuery(request, RequestOptions.DEFAULT);
            return response.getStatus().getUpdated() > 0L;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
    }

    public boolean updateByQuery(QueryBuilder query, String scriptStr, Map<String, Object> paramsMap, Boolean isRefresh) {
        boolean flag = true;
        RestClient lowClient = getClient().getLowLevelClient();
        Map<String, Object> scriptMap = new HashMap<>();
        scriptMap.put("source", scriptStr);
        scriptMap.put("lang", "painless");
        if (!CollectionUtils.isEmpty(paramsMap)) {
            scriptMap.put("params", paramsMap);
        }
        query = updateQueryWrapper(query);
        query = this.fillDomainId(query);
        String param = "{\"query\":" + query.toString() + ",\"script\":" + JSON.toJSONString(scriptMap) + "}";
        HttpEntity entity = new NStringEntity(param, ContentType.APPLICATION_JSON);
        try {
            log.info(EntityUtils.toString(entity));
            String url = "/" + getFullIndexName() + "/_update_by_query?conflicts=proceed&refresh=" + isRefresh.toString();
            Request request = new Request("POST", url);
            request.setEntity(entity);
            Response res = lowClient.performRequest(request);
            if (res.getStatusLine().getStatusCode() != 200) {
                flag = false;
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            flag = false;
            throw new MessageException(e.getMessage());
        }
        return flag;
    }


    /**
     * <b>结果转换
     *
     * @param pageNum
     * @param pageSize
     * @param totalCount
     * @param arr
     * @return
     */
    private Page<T> resultTransPage(long pageNum, long pageSize, long totalCount, JSONArray arr) {
        Page<T> page = new Page<T>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        if (totalCount == 0) {
            page.setTotalRows(totalCount);
            page.setTotalPages(1);
            page.setData(arr.toJavaList(clazz));
            return page;
        }
        long totalPages = totalCount % pageSize;
        if (totalPages == 0) {
            page.setTotalPages(totalCount / pageSize);
        } else {
            page.setTotalPages(totalCount / pageSize + 1);
        }
        page.setTotalRows(totalCount);
        page.setData(arr.toJavaList(clazz));
        return page;
    }

    /**
     * <b>属性分组 field要对照ES的Mapping Field，例如：attrs.NAME
     *
     * @param bean
     * @return
     */
    public Page<String> queryAttrVal(Long domainId, ESAttrAggBean bean) {
        Page<String> page = new Page<>();
        int pageNum = bean.getPageNum();
        int pageSize = bean.getPageSize();
        Long classId = bean.getClassId();
        String like = bean.getLike();
        String field = bean.getAttrName();
        Integer attrType = bean.getAttrType();
        RestHighLevelClient c = getClient();
        SearchRequest searchRequest = this.getSearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        int from = Math.max((pageNum - 1) * pageSize, 0);
        searchSourceBuilder.from(from);
        searchSourceBuilder.size(pageSize);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.fetchSource(false);
        BoolQueryBuilder query = new BoolQueryBuilder();
        if (like != null) {
            query.must(QueryBuilders.multiMatchQuery(like, field).operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
        }
        query.must(QueryBuilders.termQuery("classId", classId));
        searchSourceBuilder.query(query);
        if (attrType == 7) {
            field = field + "_date";
        }
        field = field + (attrType > 2 ? ".keyword" : "");
        TermsAggregationBuilder term = AggregationBuilders.terms("result").field(field).size(pageSize);
        if (attrType <= 2) {
            term.format("##########.####");
        }
        // term.size(pageSize);
        searchSourceBuilder.aggregation(term);
        List<String> data = new ArrayList<String>();
        try {
            SearchResponse searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations agg = searchResponse.getAggregations();
            Terms result = agg.get("result");
            if (result != null) {
                for (int i = 0; i < result.getBuckets().size(); i++) {
                    if (i < from || i >= from + pageSize) {
                        continue;
                    }
                    Bucket bucket = result.getBuckets().get(i);
                    if (bucket.getKeyAsString().length() > 0) {
                        // if (attrType != null && attrType.intValue() <= 2) {
                        // data.add(new
                        // BigDecimal(bucket.getKeyAsString()).toString());
                        // } else {
                        data.add(bucket.getKeyAsString());
                        // }
                    }
                }
                // for (Terms.Bucket bucket : result.getBuckets()) {
                // if (bucket.getKeyAsString().length() > 0) {
                // data.add(bucket.getKeyAsString());
                // }
                // }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        page.setData(data);
        page.setTotalPages(1);
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotalRows(data.size());
        return page;
    }

    public Page<Object> queryAttrValObj(Long domainId, ESAttrAggBean bean) {
        Page<Object> page = new Page<>();
        int pageNum = bean.getPageNum();
        int pageSize = bean.getPageSize();
        Long classId = bean.getClassId();
        String like = bean.getLike();
        String field = bean.getAttrName();
        Integer attrType = bean.getAttrType();
        RestHighLevelClient c = getClient();
        SearchRequest searchRequest = this.getSearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        int from = Math.max((pageNum - 1) * pageSize, 0);
        searchSourceBuilder.from(from);
        searchSourceBuilder.size(pageSize);
        searchRequest.source(searchSourceBuilder);
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        if (like != null) {
            query.must(QueryBuilders.multiMatchQuery(like, field).operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
        }
        query.must(QueryBuilders.termQuery("classId", classId));
        searchSourceBuilder.query(query);
        if (attrType == 7) {
            field = field + "_date";
        }
        field = field + (attrType > 2 ? ".keyword" : "");
        TermsAggregationBuilder term = AggregationBuilders.terms("result").field(field).size(pageSize);
        if (attrType <= 2) {
            term.format("##########.####");
        }
        // term.size(pageSize);
        searchSourceBuilder.aggregation(term);
        List<Object> data = new ArrayList<Object>();
        try {
            SearchResponse searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations agg = searchResponse.getAggregations();
            Terms result = agg.get("result");
            if (result != null) {
                for (int i = 0; i < result.getBuckets().size(); i++) {
                    if (i < from || i >= from + pageSize) {
                        continue;
                    }
                    Bucket bucket = result.getBuckets().get(i);
                    if (bucket.getKeyAsString().length() > 0) {
                        if (attrType <= 2) {
                            data.add(new BigDecimal(bucket.getKeyAsString()));
                        } else {
                            data.add(bucket.getKeyAsString());
                        }
                    }
                }
                // for (Terms.Bucket bucket : result.getBuckets()) {
                // if (bucket.getKeyAsString().length() > 0) {
                // data.add(bucket.getKeyAsString());
                // }
                // }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        page.setData(data);
        page.setTotalPages(1);
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotalRows(data.size());
        return page;
    }

    public void initIndex() {
        //默认一个shards
        initIndex(1);
    }

    /**
     * <b>初始化索引
     */
    public void initIndex(int number_of_shards) {
        initIndex(null, number_of_shards);
    }

    public void initIndex(List<T> list) {
        if(canInit){
            //默认一个shards
            initIndex(list, 1);
        }
    }

    public void initIndex(List<T> list,boolean canInit) {
        if(canInit){
            initIndex(list,1);
        }
    }

    /**
     * <b>初始化索引并且初始化数据
     */
    public void initIndex(List<T> list, int number_of_shards) {
        try {
            RestHighLevelClient client = getClient();
            String fullIndexName = getFullIndexName();
            GetIndexRequest getRequest = new GetIndexRequest(fullIndexName);
            boolean exists = client.indices().exists(getRequest, RequestOptions.DEFAULT);
            if (!exists) {
                CreateIndexRequest createIndexRequest = new CreateIndexRequest(fullIndexName);
                // 构建 Settings（包括分片数、字段数限制、最大结果窗口等）
                Settings settings = getSetting("my-analyzer", number_of_shards);
                createIndexRequest.settings(settings);
                // 构建 Mapping（不带 type）
                XContentBuilder mappingBuilder = getMapping("my-analyzer");
                createIndexRequest.mapping(mappingBuilder);
                // 创建索引
                client.indices().create(createIndexRequest, RequestOptions.DEFAULT);
                log.info("初始化索引成功：{}", fullIndexName);
                if (!CollectionUtils.isEmpty(list)) {
                    saveOrUpdateBatch(list);
                }
            }
        } catch (Exception e) {
            log.error("初始化索引失败: " + e.getMessage(), e);
        }
    }

    /**
     * <b> 根据查询对象查询（代码生成器生成的查询对象）
     *
     * @param pageNum
     * @param pageSize
     * @param obj
     * @return
     */
    public Page<T> getListByCdt(int pageNum, int pageSize, C obj) {
        QueryBuilder query = ESUtil.cdtToBuilder(obj);
        return getListByQuery(pageNum, pageSize, query);
    }

    /**
     * <b> 根据查询对象排序查询（代码生成器生成的查询对象）
     *
     * @param pageNum
     * @param pageSize
     * @param obj
     * @return
     */
    public Page<T> getSortListByCdt(int pageNum, int pageSize, C obj, List<SortBuilder<?>> sorts) {
        QueryBuilder query = ESUtil.cdtToBuilder(obj);
        return getSortListByQuery(pageNum, pageSize, query, sorts);
    }

    /**
     * <b> 根据查询对象排序查询（代码生成器生成的查询对象）
     *
     * @param pageNum   当前页数，从1开始
     * @param pageSize  每页条数
     * @param obj       cdt条件
     * @param sortField 排序字段，传空或空字符串则默认为id
     * @param isAsc     是否升序排序
     * @return
     */
    public Page<T> getSortListByCdt(int pageNum, int pageSize, C obj, String sortField, boolean isAsc) {
        SortOrder orderType = isAsc ? SortOrder.ASC : SortOrder.DESC;
        if (sortField == null || sortField.trim().equals("")) {
            sortField = "id";
        }
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort(sortField).order(orderType));
        return getSortListByCdt(pageNum, pageSize, obj, sorts);
    }

    /**
     * 根据查询对象查询（不分页）
     *
     * @param obj
     * @return
     */
    public List<T> getListByCdt(C obj) {
        Page<T> results = getListByCdt(1, 3000, obj);
        // 修改为日志提示避免直接抛出异常导致功能不可用
        if(results.getTotalRows() > 3000){
            log.error("不分页查询一次最多拉取3000条数据，本次查询已超出");
        }
        return results.getData();
    }

    /**
     * 根据查询对象排序查询（不分页）
     *
     * @param obj
     * @return
     */
    public List<T> getSortListByCdt(C obj, List<SortBuilder<?>> sorts) {
        Page<T> results = getSortListByCdt(1, 3000, obj, sorts);
        // 修改为日志提示避免直接抛出异常导致功能不可用
        if(results.getTotalRows() > 3000){
            log.error("不分页查询一次最多拉取3000条数据，本次查询已超出");
        }
        return results.getData();
    }

    /**
     * 保存数据的前置操作
     *
     * @param t
     */
    protected void savePreOption(T t) {
        if (t != null) {
            savePreOptionCore(t);
        }
    }

    /**
     * 保存数据的前置操作-需要的索引自行覆盖
     *
     * @param t
     */
    protected void savePreOptionCore(T t) {
    }

    /**
     * <b>保存对象
     *
     * @param t
     * @return
     */
    public Long saveOrUpdate(T t) {
        savePreOption(t);
        JSONObject json = JSON.parseObject(JSON.toJSONString(t));
        return saveOrUpdate(json, true);
    }

    /**
     * 保存对象，不立即刷新
     *
     * @param t
     * @return
     */
    public Long saveOrUpdateDelayRefresh(T t) {
        savePreOption(t);
        JSONObject json = JSON.parseObject(JSON.toJSONString(t));
        return saveOrUpdate(json, false);
    }

    /**
     * <b>批量保存
     *
     * @param list
     * @return
     */
    public Integer saveOrUpdateBatch(List<T> list) {
        // JSONArray arr = new JSONArray();
        // arr.addAll(list);
        if (BinaryUtils.isEmpty(list)) {
            return 1;
        }
        list.forEach(obj -> savePreOption(obj));
        JSONArray arr = JSON.parseArray(JSON.toJSONString(list));
        return saveOrUpdateBatch(arr, true);
    }

    /**
     * <b>批量保存不立刻刷新
     *
     * @param list
     * @return
     */
    public Integer saveOrUpdateBatchNoRefresh(List<T> list) {
        // JSONArray arr = new JSONArray();
        // arr.addAll(list);
        if (BinaryUtils.isEmpty(list)) {
            return 1;
        }
        list.forEach(obj -> savePreOption(obj));
        JSONArray arr = JSON.parseArray(JSON.toJSONString(list));
        return saveOrUpdateBatch(arr, false);
    }

    /**
     * <b>清除游标、释放ES内存
     *
     * @param scrollId 游标ID
     * @return
     */
    public Integer clearScroll(String scrollId) {
        Integer flag = 1;
        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        try {
            getClient().clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            flag = 0;
        }
        return flag;
    }

    /**
     * <b>获取游标
     *
     * @param pageNum   页码
     * @param pageSize  页大小
     * @param query     查询条件
     * @param sortField 排放字段
     * @param isAsc     是否升序
     * @return
     */
    public Map<String, Page<T>> getScrollByQuery(int pageNum, int pageSize, QueryBuilder query, String sortField, boolean isAsc) {
        JSONArray rs = new JSONArray();
        String scrollId = null;
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        Scroll scroll = new Scroll(TimeValue.timeValueMinutes(3L));
        SearchRequest searchRequest = new SearchRequest(getFullIndexName());
        searchRequest.scroll(scroll);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (isAsc) {
            searchSourceBuilder.sort(SortBuilders.fieldSort(sortField));
        } else {
            searchSourceBuilder.sort(SortBuilders.fieldSort(sortField).order(SortOrder.DESC));
        }
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        searchSourceBuilder.query(query);
        searchSourceBuilder.from(from);
        searchSourceBuilder.size(pageSize);
        // ES 7的TotalHits有最大10000条限制，加此参数可返回精确totalHist值，但对性能会影响
        searchSourceBuilder.trackTotalHits(true);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = null;
        long totalCount = 0L;
        try {
            searchResponse = getClient().search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            totalCount = searchResponse.getHits().getTotalHits().value;
            for (SearchHit hit : searchHits) {
                String res = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(res);
                rs.add(result);
            }
            scrollId = searchResponse.getScrollId();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return null;
        }
        Map<String, Page<T>> map = new HashMap<String, Page<T>>();
        Page<T> page = resultTransPage(pageNum, pageSize, totalCount, rs);
        map.put(scrollId, page);
        return map;
    }

    /**
     * 判断符合条件数据是否存在
     *
     * @param query
     * @return true:存在 false:不存在
     */
    public boolean existByCondition(QueryBuilder query) {
        Assert.notNull(query, "查询条件不可为空");
        boolean dataExist = false;
        Page<T> validResult = this.getListByQuery(1, 1, query);
        if (validResult != null && validResult.getTotalRows() > 0) {
            dataExist = true;
        }
        return dataExist;
    }

    /**
     * 统计符合条件的数据条数
     *
     * @param query
     * @return
     */
    public long countByCondition(QueryBuilder query) {
        Assert.notNull(query, "查询条件不可为空");
        Page<T> result = this.getListByQuery(1, 1, query);
        return result.getTotalRows();
    }

    /**
     * <b>根据游标获取数据（与getScrollByQuery、clearScroll配合使用）
     *
     * @param scrollId 游标ID
     * @return
     */
    public List<T> getListByScroll(String scrollId) {
        Scroll scroll = new Scroll(TimeValue.timeValueMinutes(3L));
        SearchResponse searchResponse = null;
        JSONArray rs = new JSONArray();
        try {
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(scroll);
            searchResponse = getClient().scroll(scrollRequest, RequestOptions.DEFAULT);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                String res = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(res);
                rs.add(result);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return rs.toJavaList(clazz);
    }

    /**
     * @param obj
     * @see #fillPreferencesInfo(JSONObject, SysUser)
     */
    protected void fillPreferencesInfo(JSONObject obj) {
        SysUser currentUser = null;
        try {
            currentUser = SysUtil.getCurrentUserInfo();
        } catch (LoginException e) {
            log.debug("无法获取当前登陆用户，该持久化信息可能会缺失[创建/修改]人信息和domain域信息不对的问题");
        }
        fillPreferencesInfo(obj, currentUser);
    }

    /**
     * 填补持久化信息，如创建/修改人/domainId/id/创建/修改时间
     *
     * @param obj
     */
    protected void fillPreferencesInfo(JSONObject obj, SysUser currentUser) {
        if (obj == null) {
            return;
        }
        String userLoginCode = currentUser == null ? "system" : currentUser.getLoginCode();
        Long nowTime = ESUtil.getNumberDateTime();
        Long curRecordId = null;
        if (!obj.containsKey("id") || BinaryUtils.isEmpty(obj.get("id"))) {
            // add-data处理
            obj.put("id", ESUtil.getUUID());
            obj.put("createTime", nowTime);
            if (!obj.containsKey("creator") || BinaryUtils.isEmpty(obj.get("creator"))) {
                obj.put("creator", userLoginCode);
            }
        } else {
            // update-data处理
            if (!obj.containsKey("createTime") || BinaryUtils.isEmpty(obj.get("createTime"))) {
                obj.put("createTime", nowTime);
            }
            curRecordId = obj.getLong("id");
        }
        obj.put("modifyTime", nowTime);
        obj.put("modifier", userLoginCode);
        if (!obj.containsKey("domainId") || BinaryUtils.isEmpty(obj.get("domainId"))) {
            obj.put("domainId", currentUser != null ? currentUser.getDomainId() : DEFAULT_DOMAIN_ID);
        }
        if (this.useDomainId()) {
            ESDomainQueryHelper.fillDomainId(obj);
            //对比待保存记录与库里记录的domainId
            compareCurDomainIdWithDB(curRecordId, obj);
        }
    }

    private void compareCurDomainIdWithDB(Long curRecordId, JSONObject curInfo) {
        Long curDomainId = curInfo.getLong("domainId");
        if (curDomainId == null || curRecordId == null) {
            return;
        }
        JSONObject dbInfo = getJSONObjectById(curRecordId);
        if (dbInfo == null) {
            return;
        }
        Long dbDomainId = dbInfo.getLong("domainId");
        if (dbDomainId == null) {
            return;
        }
        if (!curDomainId.equals(dbDomainId)) {
            String msg = String.format("索引[%s]下的domainId冲突，当前的domainId[%s]，库里的domainId[%s]", getFullIndexName(), curDomainId, dbDomainId);
            log.error("{}，详情：cur[{}],db[{}]", msg, JSON.toJSONString(curInfo), JSON.toJSONString(dbInfo));
            throw new BinaryException("租户信息异常[租户id不一致]");
        }
    }

    /**
     * 获取list并排重
     *
     * @param size        获取条数
     * @param query       查询条件
     * @param groupColumn 去重字段
     * @param sortCloumn  排序字段(影响去重返回那一条的结果)
     * @param asc         是否升序(影响去重返回那一条的结果)
     * @return 返回去重后(保留按照指定字段排序折叠第一条)的数据
     */
    public List<T> getListNoRepeat(int size, QueryBuilder query, String groupColumn, String sortCloumn, boolean asc) {
        JSONArray rs = new JSONArray();
        SearchRequest searchRequest = this.getSearchRequest();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = 0;
        sourceBuilder.from(from);
        sourceBuilder.size(size);
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        sourceBuilder.query(query);
        searchRequest.source(sourceBuilder);
        sourceBuilder.sort(sortCloumn, asc ? SortOrder.ASC : SortOrder.DESC);
        CollapseBuilder collapse = new CollapseBuilder(groupColumn);
        InnerHitBuilder innerHit = new InnerHitBuilder("innerData").setSize(1).addSort(SortBuilders.fieldSort(groupColumn).order(asc ? SortOrder.ASC : SortOrder.DESC));
        collapse.setInnerHits(innerHit);
        sourceBuilder.collapse(collapse);
        try {
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                rs.add(result);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return rs.toJavaList(clazz);
    }

    protected Map<String, Object> getIndexMapping() {
        try {
            RestHighLevelClient clientc = getClient();
            GetMappingsRequest mappingRequest = new GetMappingsRequest();
            mappingRequest.indices(getFullIndexName());
            GetMappingsResponse mappingResponse = clientc.indices().getMapping(mappingRequest, RequestOptions.DEFAULT);
            Map<String, MappingMetadata> allMappings = mappingResponse.mappings();
            if (allMappings.containsKey(getFullIndexName())) {
                MappingMetadata mappingMetadata = allMappings.get(getFullIndexName());
                // ES 7.x后，mapping 不再包含_type，直接返回 sourceMap
                return mappingMetadata.getSourceAsMap();
            } else {
                log.warn("索引映射不存在: {}", getFullIndexName());
            }
        } catch (Exception e) {
            log.error("获取索引映射异常", e);
        }
        return null;
    }

    /**
     * <b> 统计分类下的CI
     *
     * @param query
     * @return
     */
    public Map<Object, Long> countCIByQuery(QueryBuilder query) {
        HashMap<Object, Long> countMap = new HashMap<>();
        RestHighLevelClient c = getClient();
        SearchRequest searchRequest = getSearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        int size = 2000;
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        searchSourceBuilder.query(query);
        TermsAggregationBuilder term = AggregationBuilders.terms("classId").field("classId");
        term.size(size);
        searchSourceBuilder.aggregation(term);
        SearchResponse searchResponse = null;
        try {
            searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations agg = searchResponse.getAggregations();
            Terms rs = agg.get("classId");
            for (Bucket bucket : rs.getBuckets()) {
                countMap.put(Long.parseLong(bucket.getKeyAsString()), bucket.getDocCount());
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
        }
        return countMap;
    }

    protected Page<SearchHit> muiltyIndexQuery(String[] indexes, String[] types, QueryBuilder query, Integer pageNum, Integer pageSize, List<SortBuilder<?>> sorts) {
        SearchRequest searchRequest = new SearchRequest(indexes);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        int from = (pageNum - 1) * pageSize < 0 ? 0 : (pageNum - 1) * pageSize;
        sourceBuilder.from(from);
        sourceBuilder.size(pageSize);
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        sourceBuilder.query(query);
        for (SortBuilder<?> sort : sorts) {
            if (sort instanceof FieldSortBuilder) {
                FieldSortBuilder sortBuilder = (FieldSortBuilder)sort;
                sortBuilder.unmappedType("integer");
                sourceBuilder.sort(sortBuilder);
            } else {
                sourceBuilder.sort(sort);
            }
        }
        searchRequest.source(sourceBuilder);
        long totalCount = 0;
        List<SearchHit> datas = new ArrayList<>();
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            totalCount = response.getHits().getTotalHits().value;
            SearchHit[] hits = response.getHits().getHits();
            datas.addAll(Arrays.asList(hits));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        Page<SearchHit> page = new Page<>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setData(datas);
        page.setTotalRows(totalCount);
        if (totalCount == 0) {
            page.setTotalPages(1);
            return page;
        }
        long totalPages = totalCount % pageSize;
        if (totalPages == 0) {
            page.setTotalPages(totalCount / pageSize);
        } else {
            page.setTotalPages(totalCount / pageSize + 1);
        }
        page.setTotalRows(totalCount);
        return page;
    }

    protected SearchResponse getSearchResponseByQuery(QueryBuilder query) {
        RestHighLevelClient client = getClient();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        SearchRequest searchRequest = this.getSearchRequest();
        query = searchQueryWrapper(query);
        query = this.fillDomainId(query);
        sourceBuilder.query(query);
        searchRequest.source(sourceBuilder);
        SearchResponse response;
        try {
            response = client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return response;
    }

    protected DeleteRequest getDeleteRequest(String id) {
        return new DeleteRequest(getFullIndexName(), id);
    }

    protected IndexRequest getIndexRequest(String id) {
        return new IndexRequest(getFullIndexName()).id(id);
    }

    protected UpdateRequest getUpdateRequest(String id) {
        return new UpdateRequest(getFullIndexName(), id);
    }

    private SearchRequest getSearchRequest() {
        return new SearchRequest(getFullIndexName());
    }

    private UpdateByQueryRequest getUpdateByQueryRequest() {
        return new UpdateByQueryRequest(getFullIndexName());
    }

    protected QueryBuilder searchQueryWrapper(QueryBuilder queryBuilder) {
        return queryBuilder;
    }

    protected QueryBuilder updateQueryWrapper(QueryBuilder queryBuilder) {
        return queryBuilder;
    }

    protected void updateEmptyDomainIdRecord() {
        try {
            String fullIndexName = getFullIndexName();
            GetIndexRequest getRequest = new GetIndexRequest(fullIndexName);
            boolean existIndex = getClient().indices().exists(getRequest, RequestOptions.DEFAULT);
            if (existIndex) {
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                query.filter(QueryBuilders.scriptQuery(new Script("doc['domainId'].length==0")));
                updateByQuery(QueryBuilders.constantScoreQuery(query), "ctx._source.domainId=1", true, new HashMap<>());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
