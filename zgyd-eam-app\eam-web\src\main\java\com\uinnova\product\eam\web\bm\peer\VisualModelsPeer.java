package com.uinnova.product.eam.web.bm.peer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.lang.StringUtils;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.google.common.collect.Sets;
import com.uinnova.product.eam.base.diagram.utils.FileFilterUtil;
import com.uinnova.product.eam.base.exception.ClassConflictException;
import com.uinnova.product.eam.feign.workable.FlowableFeign;
import com.uinnova.product.eam.feign.workable.entity.*;
import com.uinnova.product.eam.model.constants.FlowableConstant;
import com.uinnova.product.eam.model.diagram.DiagramNodeLinkInfo;
import com.uinnova.product.eam.model.dto.VisualModelDiffDto;
import com.uinnova.product.eam.model.dto.VisualModelDiffResultDto;
import com.uinnova.product.eam.model.dto.VisualModelRltChangeDto;
import com.uinnova.product.eam.model.dto.VisualModelsDto;
import com.uinnova.product.eam.model.vo.ThumbnailVo;
import com.uinnova.product.eam.model.vo.VisualModelPublishedMsgVo;
import com.uinnova.product.eam.model.vo.VisualModelsVo;
import com.uinnova.product.eam.comm.model.es.ModelPanorama3D;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.eam.service.IEamNoticeService;
import com.uinnova.product.eam.service.IModelPanorama3DSvc;
import com.uinnova.product.eam.service.utils.VisualModelUtils;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.cmdb.IVisualModelApiSvc;
import com.uino.api.client.permission.IOrgApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.api.client.sys.IDictionaryApiSvc;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.sys.business.DictionaryInfoDto;
import com.uino.dao.cmdb.*;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.saas.constant.DomainReqType;
import com.uino.dao.saas.context.DomainContext;
import com.uino.dao.saas.context.DomainContextValue;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.IDirSvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.cache.ICacheService;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 元模型
 * @author: Lc
 * @create: 2021-11-04 10:49
 */
@Slf4j
@Service
public class VisualModelsPeer {

    // 编辑/取消编辑
    private static final Integer CANCEL_EDIT = 0;
    private static final Integer IS_EDIT = 1;

    private static final String VISUAL_MODEL_KEY = "VISUAL:MODEL:KEY:";

    private static final Long DEFAULT_DOMAIN_ID = 1L;

    @Autowired
    private ICacheService iCacheService;

    @Resource
    private IVisualModelApiSvc iVisualModelApiSvc;

    @Value("${local.resource.space}")
    private String localPath;

    @Autowired
    private IUserApiSvc userSvc;

    @Autowired
    private ESRoleSvc esRoleSvc;

    @Resource
    private IOrgApiSvc orgApiSvc;

    @Autowired
    private ICIClassSvc iciClassSvc;

    @Autowired
    private IRltClassSvc iRltClassSvc;

    @Autowired
    private ESRltClassSvc esRltClassSvc;

    @Autowired
    private ESVisualModelSvc visualModelSvc;

    @Autowired
    private ESVisualModelPrivateSvc visualModelPrivateSvc;

    @Resource
    private ESVisualModelHistorySvc esVisualModelHistorySvc;

    @Resource
    private ESRltInfoHistorySvc esRltInfoHistorySvc;

    @Resource
    private ESClassInfoHistorySvc esClassInfoHistorySvc;

    @Resource
    private FlowableFeign flowableFeign;

    @Resource
    private IModelPanorama3DSvc modelPanorama3DSvc;

    @Resource
    private IDirSvc iDirSvc;

    @Resource
    private IDictionaryApiSvc dictionaryApiSvc;

    @Resource
    private IEamNoticeService eamNoticeService;

    /**
     * 处理元模型编辑或取消编辑
     *
     * @param modelId
     * @param status
     * @return
     */
    public Map<String, Object> handlerVisualModels(Long modelId, Integer status) {
        if (modelId == null) {
            throw new ServiceException("元模型唯一标识不能为空!");
        }
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        if (userInfo == null) {
            throw new ServiceException("用户不存在!");
        }
        Long sysUserId = userInfo.getId();
        Map<String, Object> resultMap = new HashMap<>();

        List<ESVisualModel> esVisualModels = iVisualModelApiSvc.queryVisualModels(userInfo.getDomainId());
        List<String> visKey = new ArrayList<>();
        for (ESVisualModel esVisualModel : esVisualModels) {
            visKey.add(VISUAL_MODEL_KEY + esVisualModel.getId());
        }

        if (Objects.equals(status, IS_EDIT)) {
            // 查看当前元模型是否上锁
            Object obj = iCacheService.getCache(VISUAL_MODEL_KEY + modelId);
            if (obj != null) {
                Long cacheUserId = Long.valueOf(String.valueOf(obj));
                if (Objects.equals(sysUserId, cacheUserId)) {
                    resultMap.put("status", 1);
                    return resultMap;
                } else {
                    UserInfo user = userSvc.getUserInfoById(cacheUserId);
                    if (user != null && !StringUtils.isEmpty(user.getLoginCode())) {
                        resultMap.put("name", user.getLoginCode());
                    }
                    resultMap.put("status", 0);
                    return resultMap;
                }
            }
            // 因为元模型有多条数据 编辑时后台逻辑仅能控制当前编辑数据 但其他数据对于其他用户依然是不可编辑状态 将所有元模型上锁
            for (String vis : visKey) {
                iCacheService.setCache(vis, sysUserId, 60000*10);
            }
            // redisUtil.set(VISUAL_MODEL_KEY + modelId, sysUserId, 600);
            resultMap.put("status", 1);
            return resultMap;
        } else if (Objects.equals(status, CANCEL_EDIT)) {
            Object obj = iCacheService.getCache(VISUAL_MODEL_KEY + modelId);
            if (obj != null) {
                Long cacheUserId = Long.valueOf(String.valueOf(obj));
                if (Objects.equals(sysUserId, cacheUserId)) {
                    // 解锁全部模型
                    for (String vis : visKey) {
                        iCacheService.delKey(vis);
                    }
                    resultMap.put("status", 1);
                    return resultMap;
                } else {
                    resultMap.put("status", 0);
                    return resultMap;
                }
            }
            // 删除首次进来设置的值
            Object first = iCacheService.getCache(VISUAL_MODEL_KEY + 0);
            if (first != null) {
                iCacheService.delKey(VISUAL_MODEL_KEY + 0);
            }
            resultMap.put("status", 1);
            return resultMap;
        } else {
            throw new ServiceException("参数异常!");
        }
    }

    /**
     * 导出
     *
     * @return
     */
    public boolean exportVisualModels() {
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        if (userInfo == null) {
            throw new ServiceException("用户不存在!");
        }
        List<ESVisualModel> esVisualModels = iVisualModelApiSvc.queryVisualModels(userInfo.getDomainId());
        for (ESVisualModel model : esVisualModels) {
            boolean enable = model.getEnable();
            if (Objects.equals(enable, false)) {
                continue;
            }
            String json = model.getJson();
            String filePath = Paths.get(FileFilterUtil.parseFilePath(localPath + "/" + LocalDate.now()), UUID.randomUUID().toString() + ".json").toString();
            try {
                FileUtil.writeFile(filePath, json.getBytes());
            } catch (IOException e) {
                throw new ServiceException("导出失败");
            }
        }
        return true;
    }

    /**
     * 导入
     * @param dto
     * @return
     */
    public boolean importVisualModels(VisualModelsDto dto) {
        checkVisualModel(dto);
        try {
            /*MultipartFile jsonFile = dto.getJsonFile();
            // 获取原始名字
            String fileName = jsonFile.getOriginalFilename();
            // 获取后缀名
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            //先将.json文件转为字符串类型
            File file = new File("/" + fileName);
            //将MultipartFile类型转换为File类型
            FileUtils.copyInputStreamToFile(jsonFile.getInputStream(), file);
            String jsonString = FileUtils.readFileToString(file, "UTF-8");
            //如果是json或者txt文件
            if (".json".equals(suffixName)) {*/
            //再将json字符串转为实体类
            SysUser userInfo = SysUtil.getCurrentUserInfo();
            List<ESVisualModel> models = iVisualModelApiSvc.queryVisualModels(userInfo.getDomainId());
            for (ESVisualModel esVisualModel : models) {
                boolean enable = esVisualModel.getEnable();
                if (Objects.equals(enable, false)) {
                    continue;
                }
                Long id = esVisualModel.getId();
                if (!Objects.equals(id, dto.getVisualModelId())) {
                    throw new ServiceException("元模型数据错误!");
                }
                JSONArray originalJsonArr = JSONArray.parseArray(esVisualModel.getJson());
                JSONArray newJsonArr = new JSONArray();
                for (int i = 0; i < originalJsonArr.size(); i++) {
                    JSONObject jsonObject = originalJsonArr.getJSONObject(i);
                    Long sheetId = jsonObject.getLong("sheetId");
                    if (sheetId == null) {
                        continue;
                    }
                    newJsonArr.add(jsonObject);
                    if (Objects.equals(dto.getSheetId(), sheetId)) {
                        JSONArray addJsonArr = JSONObject.parseArray(dto.getJsonFileStr());
                        for (int j = 0; j < addJsonArr.size(); j++) {
                            JSONObject addObject = addJsonArr.getJSONObject(j);
                            addObject.put("sheetId", ESUtil.getUUID());
                            addObject.put("active", false);
                            newJsonArr.add(addObject);
                        }
                    }
                }
                if(!Objects.equals(newJsonArr.toString(), esVisualModel.getJson())) {
                    esVisualModel.setJson(newJsonArr.toString());
                    iVisualModelApiSvc.saveVisualModel(esVisualModel);
                }
            }
            //}
        } catch (Exception e) {
            throw new ServiceException("导入文件出错");
        }
        return true;
    }

    /**
     * 通过sheetId获取元模型
     * @param sheetIds
     * @param domainId
     * @return
     */
    public List<VisualModelsVo> queryVisualModels(List<Long> sheetIds, Long domainId) {
        List<ESVisualModel> models = iVisualModelApiSvc.queryVisualModels(domainId);
        List<VisualModelsVo> visualModelsVos = new ArrayList<>();
        for (ESVisualModel visualModel : models) {
            boolean enable = visualModel.getEnable();
            if (Objects.equals(enable, false)) {
                continue;
            }
            VisualModelsVo visualModelsVo = new VisualModelsVo();
            BeanUtils.copyProperties(visualModel, visualModelsVo);
            String json = visualModelsVo.getJson();
            JSONArray jsonArray = JSONArray.parseArray(json);
            JSONArray newJsonArray = new JSONArray(jsonArray.size());
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                boolean hasSheetId = jsonObject.containsKey("sheetId");
                if (!hasSheetId) {
                    continue;
                }
                Long sheetId = jsonObject.getLong("sheetId");
                if (sheetIds.contains(sheetId)) {
                    newJsonArray.add(jsonObject);
                }
            }
            visualModelsVo.setJson(newJsonArray.toString());
            String thumbnail = visualModelsVo.getThumbnail();
            if (!StringUtils.isEmpty(thumbnail) && !thumbnail.startsWith("data:image/png;base64")) {
                Map<Long, String> map = (Map) JSONObject.parseObject(thumbnail);
                Iterator<Map.Entry<Long, String>> iterator = map.entrySet().iterator();
                List<ThumbnailVo> thumbnailList = new ArrayList<>();
                while (iterator.hasNext()) {
                    Map.Entry<Long, String> next = iterator.next();
                    String oldStatusIdId = String.valueOf(next.getKey());
                    boolean contains = sheetIds.contains(Long.valueOf(oldStatusIdId));
                    if (contains) {
                        for (int i = 0; i < newJsonArray.size(); i++) {
                            JSONObject jsonObject = newJsonArray.getJSONObject(i);
                            Long sheetId = jsonObject.getLong("sheetId");
                            if (Objects.equals(sheetId, Long.valueOf(oldStatusIdId))) {
                                ThumbnailVo thumbnailVo = new ThumbnailVo();
                                thumbnailVo.setName(jsonObject.getString("name"));
                                thumbnailVo.setThumbnail(next.getValue());
                                thumbnailList.add(thumbnailVo);
                            }
                        }
                    }
                }
                visualModelsVo.setThumbnailList(thumbnailList);
            }
            visualModelsVos.add(visualModelsVo);
        }
        return visualModelsVos;
    }

    public Long saveVisualModel(ESVisualModel model) {
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        if (userInfo == null) {
            throw new ServiceException("用户不存在!");
        }
        model.setDomainId(userInfo.getDomainId());
        List<ESVisualModel> esVisualModels = iVisualModelApiSvc.queryVisualModels(userInfo.getDomainId());
        if (model.getId() != null) {
            Object obj = iCacheService.getCache(VISUAL_MODEL_KEY + model.getId());
            if (obj == null) {
                return 0L;
            }
            Long sysUserId = userInfo.getId();
            Long originalUserId = Long.valueOf(String.valueOf(obj));
            if (Objects.equals(sysUserId, originalUserId)) {
                for (ESVisualModel esVisualModel : esVisualModels) {
                    // 所有模型都上锁
                    iCacheService.setCache(VISUAL_MODEL_KEY + esVisualModel.getId(), sysUserId, 60000*10);     // 十分钟
                }
            } else {
                throw new BinaryException("无法获取用户信息，请重新登录");
            }
        }
        Long status = iVisualModelApiSvc.saveVisualModel(model);

        // 当前模型数据为新建 同原数据上锁
        if (BinaryUtils.isEmpty(model.getId())) {
            for (ESVisualModel esVisualModel : esVisualModels) {
                Object obj = iCacheService.getCache(VISUAL_MODEL_KEY + esVisualModel.getId());
                if (BinaryUtils.isEmpty(obj)) {
                    continue;
                }
                Long originalUserId = Long.valueOf(String.valueOf(obj));
                iCacheService.setCache(VISUAL_MODEL_KEY + status, originalUserId, 60000*10);
                return status;
            }
        }
        return status;
    }


    public List<SysOrg> getVisualModelOrgList() {
        SysOrg rootOrg = orgApiSvc.getRootOrg(SysUtil.getCurrentUserInfo().getDomainId());
        return orgApiSvc.getOrgListByParentId(rootOrg.getId());
    }

    public Long saveVisualModelPrivate(ESVisualModel model) {
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        if (userInfo == null) {
            throw new ServiceException("用户不存在!");
        }
        model.setDomainId(userInfo.getDomainId());
        return iVisualModelApiSvc.saveVisualModelPrivate(model);
    }

    /**
     * 发布元模型至资产库
     * @param id
     * @return
     */
    public Long publishVisualModel(Long id,String publishDescription) {

        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Long domainId = currentUserInfo.getDomainId();
        ESVisualModel esVisualModel = iVisualModelApiSvc.queryPrivateVisualModelById(domainId, id);
        //1、先发布至资产库
        Long linkPublishedId = esVisualModel.getLinkPublishedId();
        ESVisualModel designVisualModel = iVisualModelApiSvc.queryVisualModelById(domainId, linkPublishedId);
        if (designVisualModel == null) {
            //新增
            designVisualModel = new ESVisualModel();
            designVisualModel.setId(linkPublishedId);
            designVisualModel.setPublishVersion(0L);
            designVisualModel.setCreator(currentUserInfo.getLoginCode());
            designVisualModel.setCreateTime(ESUtil.getNumberDateTime());
        }
        designVisualModel.setDomainId(1L);
        designVisualModel.setEnable(Boolean.TRUE);
        designVisualModel.setName(esVisualModel.getName());
        designVisualModel.setViewRoleIds(esVisualModel.getViewRoleIds());
        designVisualModel.setEditRoleIds(esVisualModel.getEditRoleIds());
        designVisualModel.setOrgId(esVisualModel.getOrgId());
        if (designVisualModel.getHistory() && designVisualModel.getPublishVersion() == null) {
            designVisualModel.setPublishVersion(0L);
        }
        designVisualModel.setPublishVersion(designVisualModel.getPublishVersion() + 1);
        designVisualModel.setHistory(Boolean.FALSE);
        designVisualModel.setSelectedShapeGroupIds(esVisualModel.getSelectedShapeGroupIds());
        designVisualModel.setSelect(Boolean.TRUE);
        designVisualModel.setPagesetting(esVisualModel.getPagesetting());
        designVisualModel.setJson(esVisualModel.getJson());
        designVisualModel.setSourceUpdate(Boolean.FALSE);
        designVisualModel.setReferModelIds(esVisualModel.getReferModelIds());
        designVisualModel.setThumbnail(esVisualModel.getThumbnail());
        designVisualModel.setModifier(currentUserInfo.getLoginCode());
        designVisualModel.setModifyTime(ESUtil.getNumberDateTime());
        Long designVisualId = visualModelSvc.saveOrUpdate(designVisualModel);
        //2、保存元模型版本信息
        saveVisualModelVersion(designVisualModel,publishDescription);
        //3、将所有可用元模型查出来查询所有ci之间的关系然后进行更新
        iVisualModelApiSvc.flushAllEnableVisualModelCiRlt();
        //4、发布完删除本地
        delPrivateVisualModel(id);

        //5、如果是集团发布的元模型，需要发送通知
        if(Objects.equals(designVisualModel.getDomainId(), DEFAULT_DOMAIN_ID)){
            sendVisualModelChangeToOtherDomains(designVisualModel.getId());
        }
        //返回资产库id
        return designVisualId;
    }

    /**
     * 集团元模型变更后，发送消息通知其他租户
     * @param designVisualModelId
     */
    public void sendVisualModelChangeToOtherDomains(Long designVisualModelId) {

        DomainContext.setDomainContextValue(DomainContextValue.builder()
                .domainId(DEFAULT_DOMAIN_ID).domainReqType(DomainReqType.MANAGE).saas(Boolean.FALSE).build());
        ESVisualModel groupStandardVisualModel = visualModelSvc.getById(designVisualModelId);
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termQuery("referModelIds", designVisualModelId));
            boolQueryBuilder.filter(QueryBuilders.termQuery("status", 1));
            List<ESVisualModel> privateFromStandardVisualModels = visualModelPrivateSvc.getListByQueryScroll(boolQueryBuilder);
            boolQueryBuilder.filter(QueryBuilders.termQuery("enable", Boolean.TRUE));
            List<ESVisualModel> designFromStandardVisualModels = visualModelSvc.getListByQueryScroll(boolQueryBuilder);
            List<VisualModelPublishedMsgVo> visualModelPublishedMsgVos = new ArrayList<>();
            Set<String> filterPublishedVisualKey = new HashSet<>();
            //先给所有私有库的元模型发送消息
            for (ESVisualModel esVisualModel : privateFromStandardVisualModels) {
                VisualModelPublishedMsgVo visualModelPublishedMsgVo = new VisualModelPublishedMsgVo();
                visualModelPublishedMsgVo.setDomainId(esVisualModel.getDomainId());
                visualModelPublishedMsgVo.setLibType(LibType.PRIVATE.toString());
                if (org.apache.commons.lang3.StringUtils.isNotBlank(esVisualModel.getCreator())) {
                    visualModelPublishedMsgVo.setLoginCode(esVisualModel.getCreator());
                } else {
                    visualModelPublishedMsgVo.setLoginCode(esVisualModel.getModifier());
                }
                visualModelPublishedMsgVo.setGroupVisualModelId(designVisualModelId);
                visualModelPublishedMsgVo.setVisualModelId(esVisualModel.getId());
                visualModelPublishedMsgVo.setVisualModelName(esVisualModel.getName());
                visualModelPublishedMsgVo.setGroupVisualModelName(groupStandardVisualModel.getName());
                visualModelPublishedMsgVos.add(visualModelPublishedMsgVo);
                filterPublishedVisualKey.add(esVisualModel.getDomainId() + "_" + esVisualModel.getLinkPublishedId() + "_" + esVisualModel.getModifier());
                esVisualModel.setSourceUpdate(Boolean.TRUE);
            }
            if (!CollectionUtils.isEmpty(privateFromStandardVisualModels)) {
                visualModelPrivateSvc.saveOrUpdateBatch(privateFromStandardVisualModels);
            }
            //给发布的元模型的发布人发送消息，需要根据filterPublishedVisualKey做下过滤
            List<ESVisualModel> designNeedUpdateVisualModels = new ArrayList<>();
            for (ESVisualModel esVisualModel : designFromStandardVisualModels) {
                if (filterPublishedVisualKey.contains(esVisualModel.getDomainId() + "_" + esVisualModel.getId() + "_" +esVisualModel.getModifier())) {
                    continue;
                }
                VisualModelPublishedMsgVo visualModelPublishedMsgVo = new VisualModelPublishedMsgVo();
                visualModelPublishedMsgVo.setDomainId(esVisualModel.getDomainId());
                visualModelPublishedMsgVo.setLibType(LibType.DESIGN.toString());
                if (org.apache.commons.lang3.StringUtils.isNotBlank(esVisualModel.getCreator())) {
                    visualModelPublishedMsgVo.setLoginCode(esVisualModel.getCreator());
                } else {
                    visualModelPublishedMsgVo.setLoginCode(esVisualModel.getModifier());
                }
                visualModelPublishedMsgVo.setGroupVisualModelId(designVisualModelId);
                visualModelPublishedMsgVo.setVisualModelId(esVisualModel.getId());
                visualModelPublishedMsgVo.setVisualModelName(esVisualModel.getName());
                visualModelPublishedMsgVo.setGroupVisualModelName(groupStandardVisualModel.getName());
                visualModelPublishedMsgVos.add(visualModelPublishedMsgVo);
                esVisualModel.setSourceUpdate(Boolean.TRUE);
                designNeedUpdateVisualModels.add(esVisualModel);
            }
            if (!CollectionUtils.isEmpty(designNeedUpdateVisualModels)) {
                visualModelSvc.saveOrUpdateBatch(designNeedUpdateVisualModels);
            }
            if (!CollectionUtils.isEmpty(visualModelPublishedMsgVos)) {
                eamNoticeService.visualModelPublishedMsgSave(visualModelPublishedMsgVos);
            }
            log.info("集团元模型变更后，通知其他租户，受影响的元模型数量：{}", visualModelPublishedMsgVos.size());
        } catch (Exception e) {
            log.error("集团元模型GG变更后，通知其他租户失败", e);
        } finally {
            DomainContext.setDomainContextValue(DomainContextValue.builder()
                    .domainId(SysUtil.getCurrentUserInfo().getDomainId())
                    .domainReqType(DomainReqType.FRONT).saas(Boolean.TRUE).build());
        }
    }

    /**
     * 对比集团元模型与租户元模型的差异。
     * <p>
     * 该方法旨在找出指定的集团元模型与当前租户下的元模型之间，在CI分类和关系上的具体差异（新增、删除）。
     * </p>
     *
     * @param visualModelDiffDto 包含集团和租户元模型ID及SheetID的DTO
     * @return 差异对比结果，如果没有找到对应的Sheet页则返回null
     */
    public VisualModelDiffResultDto getGroupVisualModelDiff(VisualModelDiffDto visualModelDiffDto) {
        // 1. 获取模型信息
        ESVisualModel domainVisualModel = getDomainVisualModel(visualModelDiffDto);
        ESVisualModel groupVisualModel = getGroupVisualModel(visualModelDiffDto.getGroupVisualModelId());

        // 2. 从模型JSON中提取指定的Sheet页
        JSONObject groupSheetJson = getSheetJson(groupVisualModel.getJson(), visualModelDiffDto.getGroupSheetId());
        JSONObject domainSheetJson = getSheetJson(domainVisualModel.getJson(), visualModelDiffDto.getDomainSheetId());

        if (groupSheetJson == null || domainSheetJson == null) {
            log.warn("未能找到对应的元模型Sheet页, 集团SheetId:{}, 租户SheetId:{}",
                    visualModelDiffDto.getGroupSheetId(), visualModelDiffDto.getDomainSheetId());
            return null;
        }

        Map<Long, JSONObject> classJsonMap = new HashMap<>();
        Map<Long, JSONObject> rltJsonMap = new HashMap<>();

        JSONArray nodeDataArray = groupSheetJson.getJSONArray("nodeDataArray");
        if (!BinaryUtils.isEmpty(nodeDataArray)) {
            for (int j = 0; j < nodeDataArray.size(); j++) {
                JSONObject nodeData = nodeDataArray.getJSONObject(j);
                if (nodeData.getLong("classId") != null) {
                    classJsonMap.put(nodeData.getLong("classId"), nodeData);
                }
            }
        }
        JSONArray rltDataArray = groupSheetJson.getJSONArray("linkDataArray");
        if (!BinaryUtils.isEmpty(rltDataArray)) {
            for (int j = 0; j < rltDataArray.size(); j++) {
                JSONObject rltData = rltDataArray.getJSONObject(j);
                if (rltData.getLong("classId") != null) {
                    rltJsonMap.put(rltData.getLong("classId"), rltData);
                }
            }
        }

        // 3. 提取并对比CI分类差异
        Map<String, List<CcCiClass>> ciChangeMap = compareCiDifferences(groupSheetJson, domainSheetJson);

        // 4. 提取并对比关系差异
        Map<String, List<VisualModelRltChangeDto>> rltChangeMap = compareRltDifferences(groupSheetJson, domainSheetJson);

        // 5. 封装并返回结果
        VisualModelDiffResultDto visualModelDiffResultDto = new VisualModelDiffResultDto(groupVisualModel.getName(), domainVisualModel.getName(), ciChangeMap, rltChangeMap);
        visualModelDiffResultDto.setClassJsonMap(classJsonMap);
        visualModelDiffResultDto.setRltJsonMap(rltJsonMap);
        return visualModelDiffResultDto;
    }

    /**
     * 同步集团分类至当前租户下
     *
     * @param id
     * @return
     */
    public Long syncGroupClassToCurrentDomain(Long id) {
        if (DEFAULT_DOMAIN_ID.equals(DomainContext.getDomainContextValue().getDomainId())) {
            throw new BinaryException("当前属于集团租户，无需同步");
        }

        Long originalDomainId = SysUtil.getCurrentUserInfo().getDomainId();
        try {
            // 设置为集团租户
            DomainContext.setDomainContextValue(DomainContextValue.builder()
                    .domainId(DEFAULT_DOMAIN_ID).domainReqType(DomainReqType.MANAGE).saas(Boolean.FALSE).build());
            CcCiClassInfo ccCiClassInfo = iciClassSvc.queryClassInfoById(id);
            if (ccCiClassInfo == null) {
                throw new BinaryException("集团下不存在该分类, ID: " + id);
            }

            // 设置为当前租户
            DomainContext.setDomainContextValue(DomainContextValue.builder()
                    .domainId(originalDomainId).domainReqType(DomainReqType.FRONT).saas(Boolean.TRUE).build());

            CcCiClassInfo ciClassByClassCode = null;
            try {
                ciClassByClassCode = iciClassSvc.getCiClassByClassCode(ccCiClassInfo.getCiClass().getClassCode());
            } catch (Exception e) {
                log.error("查询本地分类失败", e);
            }

            if (ciClassByClassCode != null) {
                Boolean fromStandard = ciClassByClassCode.getCiClass().getFromStandard();
                if (fromStandard) {
                    Long rootId = ciClassByClassCode.getCiClass().getRootId();
                    if (ccCiClassInfo.getCiClass().getId().equals(rootId)) {
                        // 已经同步过了，直接返回
                        return ciClassByClassCode.getCiClass().getId();
                    } else {
                        throw new BinaryException("当前分类已存在本地，但有冲突，请先本地分类删除后在进行同步");
                    }
                } else {
                    throw new BinaryException("当前分类已存在本地，请先本地分类删除后在进行同步");
                }
            } else {
                // 将分类及其所有依赖项同步到本地
                try {
                    Set<Long> idsToSync = Collections.singleton(id);
                    // 调用重构后的、更健壮的同步方法
                    Map<Long, Long> idMap = syncCiClassesWithDependencies(idsToSync, originalDomainId);
                    // 从返回的映射中获取新创建的CI的ID
                    Long newId = idMap.get(id);
                    if (newId == null) {
                        log.error("同步集团分类到当前租户后未能获取新ID，分类ID: {}", id);
                        throw new BinaryException("同步分类失败: 未能获取新ID");
                    }
                    return newId;
                } catch (ClassConflictException e) {
                    log.error("同步集团分类到当前租户失败，分类ID: {}, 存在命名冲突: {}", id, e.getMessage(), e);
                    throw new BinaryException("同步分类失败: 存在命名冲突的分类 " + e.getMessage() + "，请先处理冲突后再同步。");
                } catch (Exception e) {
                    log.error("同步集团分类到当前租户失败，分类ID: {}, 错误信息: {}", id, e.getMessage(), e);
                    throw new BinaryException("同步分类失败: " + e.getMessage());
                }
            }
        } finally {
            // 确保上下文被重置
            DomainContext.setDomainContextValue(DomainContextValue.builder()
                    .domainId(originalDomainId).domainReqType(DomainReqType.FRONT).saas(Boolean.TRUE).build());
        }
    }

    public Long syncGroupRltToCurrentDomain(Long rltId) {
        if (DEFAULT_DOMAIN_ID.equals(DomainContext.getDomainContextValue().getDomainId())) {
            throw new BinaryException("当前属于集团租户，无需同步");
        }
        DomainContext.setDomainContextValue(DomainContextValue.builder()
                .domainId(DEFAULT_DOMAIN_ID).domainReqType(DomainReqType.MANAGE).saas(Boolean.FALSE).build());
        CCcCiClass rltCdt = new CCcCiClass();
        rltCdt.setId(rltId);
        rltCdt.setDomainId(DEFAULT_DOMAIN_ID);
        List<CcCiClassInfo> rltClassByCdt = iRltClassSvc.getRltClassByCdt(rltCdt);

        if (CollectionUtils.isEmpty(rltClassByCdt)) {
            throw new BinaryException("关系在集团下不存在,请检查");
        }
        CcCiClassInfo groupRltClass = rltClassByCdt.get(0);

        //设置为当前租户
        DomainContext.setDomainContextValue(DomainContextValue.builder()
                .domainId(SysUtil.getCurrentUserInfo().getDomainId()).domainReqType(DomainReqType.FRONT).saas(Boolean.TRUE).build());

        CcCiClassInfo rltClassByName = iRltClassSvc.getRltClassByName(SysUtil.getCurrentUserInfo().getDomainId(), groupRltClass.getCiClass().getClassName());
        if (rltClassByName != null) {
            return rltClassByName.getCiClass().getId();
        } else {
            //同步关系至当前租户下
            long newRltId = ESUtil.getUUID();
            ESCIClassInfo esciClassInfo = new ESCIClassInfo();
            BeanUtils.copyProperties(groupRltClass.getCiClass(), esciClassInfo);
            List<ESCIAttrDefInfo> esAttrDefs = groupRltClass.getAttrDefs().stream().map(attr -> {
                ESCIAttrDefInfo esAttr = new ESCIAttrDefInfo();
                BeanUtils.copyProperties(attr, esAttr);
                attr.setId(ESUtil.getUUID());
                attr.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
                attr.setClassId(newRltId);
                return esAttr;
            }).collect(Collectors.toList());
            esciClassInfo.setRootId(groupRltClass.getCiClass().getId());
            esciClassInfo.setFromStandard(Boolean.TRUE);
            esciClassInfo.setAttrDefs(esAttrDefs);
            esciClassInfo.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
            esciClassInfo.setId(newRltId);
            // 3. 保存关系
            return iRltClassSvc.saveOrUpdate(esciClassInfo);
        }

    }


    public ESVisualModel getGroupStandardVisualModel(Long visualModelId) {
        //切换到集团租户
        DomainContext.setDomainContextValue(DomainContextValue.builder()
                .domainId(DEFAULT_DOMAIN_ID).domainReqType(DomainReqType.MANAGE).saas(Boolean.FALSE).build());
        ESVisualModel groupVisualModel = visualModelSvc.getById(visualModelId);
        if(groupVisualModel.getGroupStandard()){
            return groupVisualModel;
        }else {
            throw new BinaryException("当前元模型非集团标准元模型");
        }
    }

    /**
     * 获取当前租户的元模型实体。
     *
     * @param visualModelDiffDto DTO
     * @return ESVisualModel 租户元模型
     */
    private ESVisualModel getDomainVisualModel(VisualModelDiffDto visualModelDiffDto) {
        if (LibType.PRIVATE.equals(visualModelDiffDto.getLibType())) {
            return visualModelPrivateSvc.getById(visualModelDiffDto.getDomainVisualModelId());
        } else {
            return visualModelSvc.getById(visualModelDiffDto.getDomainVisualModelId());
        }
    }

    /**
     * 获取集团的元模型实体。
     *
     * @param groupVisualModelId 集团元模型ID
     * @return ESVisualModel 集团元模型
     */
    private ESVisualModel getGroupVisualModel(Long groupVisualModelId) {
        DomainContext.setDomainContextValue(DomainContextValue.builder()
                .domainId(DEFAULT_DOMAIN_ID).domainReqType(DomainReqType.MANAGE).saas(Boolean.FALSE).build());
        return visualModelSvc.getById(groupVisualModelId);
    }

    /**
     * 从JSON字符串中根据sheetId提取对应的JSONObject。
     *
     * @param jsonStr JSON数组字符串
     * @param sheetId 要查找的Sheet ID
     * @return 对应的JSONObject，未找到则返回null
     */
    private JSONObject getSheetJson(String jsonStr, String sheetId) {
        JSONArray jsonArr = JSON.parseArray(jsonStr);
        for (int i = 0; i < jsonArr.size(); i++) {
            JSONObject jsonObject = jsonArr.getJSONObject(i);
            String currentSheetId = jsonObject.getString("sheetId");
            if (sheetId.equalsIgnoreCase(currentSheetId)) {
                return jsonObject;
            }
        }
        return null;
    }

    /**
     * 对比CI分类的差异。
     *
     * @param groupSheetJson  集团Sheet JSON
     * @param domainSheetJson 租户Sheet JSON
     * @return 一个包含"add"和"del"键的Map，值为对应的CI分类列表
     */
    private Map<String, List<CcCiClass>> compareCiDifferences(JSONObject groupSheetJson, JSONObject domainSheetJson) {
        // 提取集团CI信息
        NodeInfo groupNodeInfo = extractNodeInfo(groupSheetJson);
        Map<Long, CcCiClassInfo> groupAllCiClassInfoMap = fetchCiClassInfo(DEFAULT_DOMAIN_ID, groupNodeInfo.classIds);

        // 提取租户CI信息
        NodeInfo domainNodeInfo = extractNodeInfo(domainSheetJson);
        DomainCiInfo domainCiInfo = fetchDomainCiInfo(domainNodeInfo.classIds);

        // 计算差异
        Set<Long> addCiClassSet = Sets.difference(groupNodeInfo.classIds, domainCiInfo.fromStandardClassIdSet);
        Set<Long> delCiClassSet = Sets.difference(domainCiInfo.fromStandardClassIdSet, groupNodeInfo.classIds);

        // 封装结果
        Map<String, List<CcCiClass>> ciChangeMap = new HashMap<>();
        List<CcCiClass> addCiClasses = addCiClassSet.stream()
                .map(id -> groupAllCiClassInfoMap.get(id).getCiClass())
                .collect(Collectors.toList());
        ciChangeMap.put("add", addCiClasses);

        List<CcCiClass> delCiClasses = delCiClassSet.stream()
                .map(id -> domainCiInfo.standardClassIdMap.get(id).getCiClass())
                .collect(Collectors.toList());
        ciChangeMap.put("del", delCiClasses);

        return ciChangeMap;
    }

    /**
     * 对比关系的差异。
     *
     * @param groupSheetJson  集团Sheet JSON
     * @param domainSheetJson 租户Sheet JSON
     * @return 一个包含"add"和"del"键的Map，值为对应的关系变更DTO列表
     */
    private Map<String, List<VisualModelRltChangeDto>> compareRltDifferences(JSONObject groupSheetJson, JSONObject domainSheetJson) {
        // 准备CI信息，用于关系查找
        NodeInfo groupNodeInfo = extractNodeInfo(groupSheetJson);
        Map<Long, CcCiClassInfo> groupAllCiClassInfoMap = fetchCiClassInfo(DEFAULT_DOMAIN_ID, groupNodeInfo.classIds);
        NodeInfo domainNodeInfo = extractNodeInfo(domainSheetJson);
        DomainCiInfo domainCiInfo = fetchDomainCiInfo(domainNodeInfo.classIds);

        // 提取集团关系信息
        LinkInfo groupLinkInfo = extractLinkInfo(groupSheetJson, groupNodeInfo.nodeKeyMap);
        Map<String, CcCiClassInfo> allGroupRltMap = fetchRltClassInfo(DEFAULT_DOMAIN_ID, groupLinkInfo.rltCodes);

        // 提取租户关系信息
        LinkInfo domainLinkInfo = extractLinkInfo(domainSheetJson, domainNodeInfo.nodeKeyMap, domainCiInfo.idAndRootIdMap);
        Map<String, CcCiClassInfo> domainRltMap = fetchRltClassInfo(SysUtil.getCurrentUserInfo().getDomainId(), domainLinkInfo.rltCodes);


        // 计算差异
        Set<String> addRltFilterSet = Sets.difference(groupLinkInfo.rltFilterSet, domainLinkInfo.rltFilterSet);
        Set<String> delRltFilterSet = Sets.difference(domainLinkInfo.rltFilterSet, groupLinkInfo.rltFilterSet);

        // 封装结果
        List<VisualModelRltChangeDto> addRltChangeDtos = addRltFilterSet.stream()
                .map(filterCode -> buildRltChangeDto(filterCode, allGroupRltMap, groupAllCiClassInfoMap))
                .collect(Collectors.toList());

        List<VisualModelRltChangeDto> delRltChangeDtos = delRltFilterSet.stream()
                .map(filterCode -> buildRltChangeDto(filterCode, domainRltMap, domainCiInfo.domainGroupAllClassMap, true))
                .collect(Collectors.toList());

        Map<String, List<VisualModelRltChangeDto>> rltChangeMap = new HashMap<>();
        rltChangeMap.put("add", addRltChangeDtos);
        rltChangeMap.put("del", delRltChangeDtos);

        return rltChangeMap;
    }

    /**
     * 从Sheet JSON中提取节点信息。
     */
    private NodeInfo extractNodeInfo(JSONObject sheetJson) {
        JSONArray nodeArray = sheetJson.getJSONArray("nodeDataArray");
        Set<Long> classIds = new HashSet<>();
        Map<Integer, JSONObject> nodeKeyMap = new HashMap<>();
        for (int i = 0; i < nodeArray.size(); i++) {
            JSONObject node = nodeArray.getJSONObject(i);
            if (node.get("classId") != null) {
                classIds.add(node.getLong("classId"));
                nodeKeyMap.put(node.getInteger("key"), node);
            }
        }
        return new NodeInfo(classIds, nodeKeyMap);
    }

    /**
     * 从Sheet JSON中提取关系连线信息。
     */
    private LinkInfo extractLinkInfo(JSONObject sheetJson, Map<Integer, JSONObject> nodeKeyMap) {
        return extractLinkInfo(sheetJson, nodeKeyMap, null);
    }

    /**
     * 从Sheet JSON中提取关系连线信息（租户用，需要ID映射）。
     */
    private LinkInfo extractLinkInfo(JSONObject sheetJson, Map<Integer, JSONObject> nodeKeyMap, Map<Long, Long> idAndRootIdMap) {
        JSONArray linkArray = sheetJson.getJSONArray("linkDataArray");
        Set<String> rltFilterSet = new HashSet<>();
        Set<String> rltCodes = new HashSet<>();
        for (int i = 0; i < linkArray.size(); i++) {
            JSONObject link = linkArray.getJSONObject(i);
            String rltClassCode = link.getString("classCode");
            if (StringUtils.isBlank(rltClassCode)) continue;

            rltCodes.add(rltClassCode);
            Integer fromNodeKey = link.getInteger("from");
            Integer toNodeKey = link.getInteger("to");
            JSONObject fromNodeJson = nodeKeyMap.get(fromNodeKey);
            JSONObject toNodeJson = nodeKeyMap.get(toNodeKey);
            if(fromNodeJson == null || toNodeJson == null) continue;

            Long fromClassId = fromNodeJson.getLong("classId");
            Long toClassId = toNodeJson.getLong("classId");

            // 如果是租户，需要将自己的ID映射回集团ID进行比较
            if (idAndRootIdMap != null) {
                fromClassId = idAndRootIdMap.get(fromClassId);
                toClassId = idAndRootIdMap.get(toClassId);
            }

            if (fromClassId != null && toClassId != null) {
                rltFilterSet.add(fromClassId + "_" + rltClassCode + "_" + toClassId);
            }
        }
        return new LinkInfo(rltFilterSet, rltCodes);
    }


    /**
     * 根据指定的Class ID集合，获取CI的完整信息。
     *
     * @param domainId  租户ID
     * @param classIds  CI Class ID 集合
     * @return Map, Key为Class ID, Value为CcCiClassInfo
     */
    private Map<Long, CcCiClassInfo> fetchCiClassInfo(Long domainId, Set<Long> classIds) {
        if (CollectionUtils.isEmpty(classIds)) {
            return Collections.emptyMap();
        }
        CCcCiClass cdt = new CCcCiClass();
        cdt.setDomainId(domainId);
        cdt.setIds(classIds.toArray(new Long[0]));
        return iciClassSvc.queryClassByCdt(cdt).stream()
                .collect(Collectors.toMap(info -> info.getCiClass().getId(), info -> info));
    }

    /**
     * 获取当前租户的CI信息，并建立与集团CI的映射关系。
     *
     * @param domainClassIdSet 当前租户元模型中使用的CI Class ID集合
     * @return DomainCiInfo 包含映射关系的封装对象
     */
    private DomainCiInfo fetchDomainCiInfo(Set<Long> domainClassIdSet) {
        Map<Long, CcCiClassInfo> standardClassIdMap = new HashMap<>();
        Map<Long, Long> idAndRootIdMap = new HashMap<>();
        Map<Long, CcCiClassInfo> domainGroupAllClassMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(domainClassIdSet)) {
            CCcCiClass domainCdt = new CCcCiClass();
            domainCdt.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
            domainCdt.setIds(domainClassIdSet.toArray(new Long[0]));
            List<CcCiClassInfo> domainCiClassInfos = iciClassSvc.queryClassByCdt(domainCdt);

            for (CcCiClassInfo domainCiClassInfo : domainCiClassInfos) {
                CcCiClass ciClass = domainCiClassInfo.getCiClass();
                if (ciClass.getFromStandard()) {
                    standardClassIdMap.put(ciClass.getRootId(), domainCiClassInfo);
                    idAndRootIdMap.put(ciClass.getId(), ciClass.getRootId());
                }
                domainGroupAllClassMap.put(ciClass.getRootId(), domainCiClassInfo);
            }
        }
        return new DomainCiInfo(standardClassIdMap.keySet(), standardClassIdMap, idAndRootIdMap, domainGroupAllClassMap);
    }

    /**
     * 根据指定的关系Code集合，获取关系的完整信息。
     *
     * @param domainId 租户ID
     * @param rltCodes 关系Code集合
     * @return Map, Key为关系Code, Value为CcCiClassInfo
     */
    private Map<String, CcCiClassInfo> fetchRltClassInfo(Long domainId, Set<String> rltCodes) {
        if (CollectionUtils.isEmpty(rltCodes)) {
            return Collections.emptyMap();
        }
        CCcCiClass rltCdt = new CCcCiClass();
        rltCdt.setDomainId(domainId);
        rltCdt.setClassCodes(rltCodes.toArray(new String[0]));
        return iRltClassSvc.getRltClassByCdt(rltCdt).stream()
                .collect(Collectors.toMap(info -> info.getCiClass().getClassCode(), info -> info));
    }


    /**
     * 构建关系变更DTO。
     */
    private VisualModelRltChangeDto buildRltChangeDto(String filterCodeData, Map<String, CcCiClassInfo> rltMap, Map<Long, CcCiClassInfo> classMap) {
        return buildRltChangeDto(filterCodeData, rltMap, classMap, false);
    }

    /**
     * 构建关系变更DTO。
     *
     * @param filterCodeData      格式为 "fromClassId_rltCode_toClassId" 的字符串
     * @param rltMap              关系信息Map (key: rltCode)
     * @param classMap            CI分类信息Map (key: classId or rootId)
     * @param isDeletion          是否为删除操作（影响从哪个Map获取CI信息）
     * @return VisualModelRltChangeDto
     */
    private VisualModelRltChangeDto buildRltChangeDto(String filterCodeData, Map<String, CcCiClassInfo> rltMap, Map<Long, CcCiClassInfo> classMap, boolean isDeletion) {
        VisualModelRltChangeDto dto = new VisualModelRltChangeDto();
        String[] split = filterCodeData.split("_");
        long fromClassId = Long.parseLong(split[0]);
        String rltCode = split[1];
        long toClassId = Long.parseLong(split[2]);

        dto.setRltClass(rltMap.get(rltCode).getCiClass());

        // 对于删除操作，source/target Class是租户的CI，其ID就是classMap的key
        // 对于新增操作，source/target Class是集团的CI, 其ID也是classMap的key
        dto.setSourceClass(classMap.get(fromClassId).getCiClass());
        dto.setTargetClass(classMap.get(toClassId).getCiClass());
        return dto;
    }


    /**
     * 内部类，用于封装节点信息。
     */
    private static class NodeInfo {
        final Set<Long> classIds;
        final Map<Integer, JSONObject> nodeKeyMap;

        NodeInfo(Set<Long> classIds, Map<Integer, JSONObject> nodeKeyMap) {
            this.classIds = classIds;
            this.nodeKeyMap = nodeKeyMap;
        }
    }

    /**
     * 内部类，用于封装关系连线信息。
     */
    private static class LinkInfo {
        final Set<String> rltFilterSet;
        final Set<String> rltCodes;

        LinkInfo(Set<String> rltFilterSet, Set<String> rltCodes) {
            this.rltFilterSet = rltFilterSet;
            this.rltCodes = rltCodes;
        }
    }

    /**
     * 内部类，用于封装租户侧与集团标准关联的CI信息。
     */
    private static class DomainCiInfo {
        final Set<Long> fromStandardClassIdSet;
        final Map<Long, CcCiClassInfo> standardClassIdMap;
        final Map<Long, Long> idAndRootIdMap;
        final Map<Long, CcCiClassInfo> domainGroupAllClassMap;

        DomainCiInfo(Set<Long> fromStandardClassIdSet, Map<Long, CcCiClassInfo> standardClassIdMap, Map<Long, Long> idAndRootIdMap, Map<Long, CcCiClassInfo> domainGroupAllClassMap) {
            this.fromStandardClassIdSet = fromStandardClassIdSet;
            this.standardClassIdMap = standardClassIdMap;
            this.idAndRootIdMap = idAndRootIdMap;
            this.domainGroupAllClassMap = domainGroupAllClassMap;
        }
    }

    /**
     * 记录元模型发布版本数据
     * @return
     */
    public Long saveVisualModelVersion(ESVisualModel designVisualModel,String publishDescription) {
        Long id = designVisualModel.getId();
        //首先记录下元模型的版本信息
        ESVisualModelHistory esVisualModelHistory = new ESVisualModelHistory();
        BeanUtils.copyProperties(designVisualModel, esVisualModelHistory);
        esVisualModelHistory.setId(null);
        esVisualModelHistory.setLinkPublishedId(id);
        esVisualModelHistory.setCreateTime(ESUtil.getNumberDate());
        esVisualModelHistory.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
        esVisualModelHistory.setPublishDescription(publishDescription);
        Long publishVersionId = esVisualModelHistorySvc.saveOrUpdate(esVisualModelHistory);
        //查询元模型的ci信息和关系信息并记录
        String json = esVisualModelHistory.getJson();
        JSONArray objects = JSON.parseArray(json);

        Set<Long> visualRltId = new HashSet<>();
        Set<Long> visualClassId = new HashSet<>();

        for (int i = 0; i < objects.size(); i++) {
            JSONObject object = objects.getJSONObject(i);
            JSONArray linkDataArray = object.getJSONArray("linkDataArray");
            JSONArray nodeDataArray = object.getJSONArray("nodeDataArray");
            for (int j = 0; j < linkDataArray.size(); j++) {
                JSONObject linkData = linkDataArray.getJSONObject(j);
                if (linkData.get("classId") != null) {
                    visualRltId.add(linkData.getLong("classId"));
                }
            }
            for (int j = 0; j < nodeDataArray.size(); j++) {
                JSONObject nodeData = nodeDataArray.getJSONObject(j);
                if (nodeData.get("classId") != null) {
                    visualClassId.add(nodeData.getLong("classId"));
                }
            }
        }

        List<ClassInfoHistory> classInfoHistories = new ArrayList<>();
        List<RltInfoHistory> rltInfoHistories = new ArrayList<>();

        if (!CollectionUtils.isEmpty(visualClassId)) {
            CCcCiClass cCcCiClass = new CCcCiClass();
            cCcCiClass.setIds(visualClassId.toArray(new Long[0]));
            List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(cCcCiClass);

            if(!CollectionUtils.isEmpty(ccCiClassInfos)){
                for (CcCiClassInfo ccCiClassInfo : ccCiClassInfos) {
                    ClassInfoHistory classInfoHistory = new ClassInfoHistory();
                    classInfoHistory.setCiClass(ccCiClassInfo.getCiClass());
                    classInfoHistory.setAttrDefs(ccCiClassInfo.getAttrDefs());
                    classInfoHistory.setClassId(ccCiClassInfo.getCiClass().getId());
                    classInfoHistory.setClassCode(ccCiClassInfo.getCiClass().getClassCode());
                    classInfoHistory.setVisualPublishVersionId(publishVersionId);
                    classInfoHistories.add(classInfoHistory);
                }
                esClassInfoHistorySvc.saveOrUpdateBatch(classInfoHistories);
            }
        }
        if (!CollectionUtils.isEmpty(visualRltId)) {
            CCcCiClass cCcCiClass = new CCcCiClass();
            cCcCiClass.setIds(visualRltId.toArray(new Long[0]));
            List<CcCiClassInfo> rltClassByCdt = iRltClassSvc.getRltClassByCdt(cCcCiClass);
            if(!CollectionUtils.isEmpty(rltClassByCdt)){
                for (CcCiClassInfo ccCiClassInfo : rltClassByCdt) {
                    RltInfoHistory rltInfoHistory = new RltInfoHistory();
                    rltInfoHistory.setRltClass(ccCiClassInfo.getCiClass());
                    rltInfoHistory.setAttrDefs(ccCiClassInfo.getAttrDefs());
                    rltInfoHistory.setRltId(ccCiClassInfo.getCiClass().getId());
                    rltInfoHistory.setRltCode(ccCiClassInfo.getCiClass().getClassCode());
                    rltInfoHistory.setVisualPublishVersionId(publishVersionId);
                    rltInfoHistories.add(rltInfoHistory);
                }
                esRltInfoHistorySvc.saveOrUpdateBatch(rltInfoHistories);
            }
        }
        return publishVersionId;
    }

    public LinkedList<ESVisualModelVo> getVisualModelList() {
        //获取所有已发布的元模型

        List<ESVisualModelVo> designVisualModels = iVisualModelApiSvc.queryVisualModelsNoChickExit(DomainContext.getDomainContextValue().getDomainId(), LibType.DESIGN, null);
        List<ESVisualModelVo> privateVisualModels = iVisualModelApiSvc.queryVisualModelsNoChickExit(DomainContext.getDomainContextValue().getDomainId(), LibType.PRIVATE, SysUtil.getCurrentUserInfo().getLoginCode());

        //获取当前用户的角色id
        Set<Long> roleIdsByUserId = esRoleSvc.getRoleIdsByUserId(SysUtil.getCurrentUserInfo().getId());

        Map<Long, Long> visualModelVersionMap = new HashMap<>();
        Iterator<ESVisualModelVo> iterator = designVisualModels.iterator();
        while (iterator.hasNext()){
            ESVisualModelVo designVisualModel = iterator.next();
            List<Long> editRoleIds = designVisualModel.getEditRoleIds();
            List<Long> viewRoleIds = designVisualModel.getViewRoleIds();
            //判断用户角色跟是否在可编辑和可查看角色中（交集是否有值）
            Set<Long> mergeRoleIds = new HashSet<>();
            if(!CollectionUtils.isEmpty(editRoleIds)){
                mergeRoleIds.addAll(editRoleIds);
            }
            if(!CollectionUtils.isEmpty(viewRoleIds)){
                mergeRoleIds.addAll(viewRoleIds);
            }
            if (CollectionUtils.isEmpty(Sets.intersection(roleIdsByUserId, mergeRoleIds))){
                iterator.remove();
            }else {
                visualModelVersionMap.put(designVisualModel.getId(), designVisualModel.getPublishVersion());
            }
        }

        for (ESVisualModelVo privateVisualModel : privateVisualModels) {
            Long publishVersion = visualModelVersionMap.get(privateVisualModel.getLinkPublishedId());
            if (publishVersion != null) {
                if (privateVisualModel.getPublishVersion()==null||!privateVisualModel.getPublishVersion().equals(publishVersion)) {
                    privateVisualModel.setOldVersion(Boolean.TRUE);
                }
            }
        }

        // 查询所有元模型的3D全景配置
        Set<Long> modelIds = new HashSet<>(visualModelVersionMap.keySet());
        if (!CollectionUtils.isEmpty(modelIds)) {
            Map<Long, ModelPanorama3D> panoramaMap = modelPanorama3DSvc.queryByModelIds(modelIds);
            if (!CollectionUtils.isEmpty(panoramaMap)) {
                // 设置3D全景配置ID到元模型对象中
                for (ESVisualModelVo designVisualModel : designVisualModels) {
                    ModelPanorama3D panorama = panoramaMap.get(designVisualModel.getId());
                    if (panorama != null) {
                        designVisualModel.setModelPanorama3DId(panorama.getId());
                    }
                }
            }
        }

        LinkedList<ESVisualModelVo> resultList = new LinkedList<>();

        resultList.addAll(privateVisualModels);
        resultList.addAll(designVisualModels);
        return resultList;
    }

    public Long getVisualModelPrivate(Long visId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("linkPublishedId", visId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("status", 1));
        boolQueryBuilder.filter(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
        List<ESVisualModel> visualModelByQuery = iVisualModelApiSvc.getVisualModelByQuery(boolQueryBuilder, LibType.PRIVATE);
        if (!CollectionUtils.isEmpty(visualModelByQuery)) {
            if (visualModelByQuery.get(0).getApprove() != 0) {
                throw new BinaryException("存在审批中模型，不可编辑");
            }
            return visualModelByQuery.get(0).getId();
        } else {
            //检出到当前用户本地一份元模型进行编辑
            BoolQueryBuilder designQueryBuilder = QueryBuilders.boolQuery();
            designQueryBuilder.filter(QueryBuilders.termQuery("id", visId));
            List<ESVisualModel> designVisualList = iVisualModelApiSvc.getVisualModelByQuery(designQueryBuilder, LibType.DESIGN);
            if (CollectionUtils.isEmpty(designVisualList)) {
                throw new BinaryException("未查到元模型");
            }
            ESVisualModel designVisualModel = designVisualList.get(0);
            ESVisualModel privateVisualModel = new ESVisualModel();
            BeanUtils.copyProperties(designVisualModel, privateVisualModel);
            privateVisualModel.setId(null);
            privateVisualModel.setLinkPublishedId(designVisualModel.getId());
            privateVisualModel.setStatus(1);
            return visualModelPrivateSvc.saveOrUpdate(privateVisualModel);
        }
    }

    public ESVisualModel getVisualModelPrivateById(Long privateVisId) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("id", privateVisId));
        List<ESVisualModel> visualModelByQuery = iVisualModelApiSvc.getVisualModelByQuery(queryBuilder, LibType.PRIVATE);
        if(CollectionUtils.isEmpty(visualModelByQuery)){
            return null;
        }else {
            return visualModelByQuery.get(0);
        }
    }

    public ESVisualModel getVisualModelById(Long visualModelId) {
        ESVisualModel visualModelPrivateById = getVisualModelPrivateById(visualModelId);
        ESVisualModelVo esVisualModelVo = new ESVisualModelVo();
        if(visualModelPrivateById==null){
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            queryBuilder.filter(QueryBuilders.termQuery("id", visualModelId));
            List<ESVisualModel> visualModelByQuery = iVisualModelApiSvc.getVisualModelByQuery(queryBuilder, LibType.DESIGN);
            if(CollectionUtils.isEmpty(visualModelByQuery)){
                return null;
            }else {
                BeanUtils.copyProperties(visualModelByQuery.get(0),esVisualModelVo);
                esVisualModelVo.setLibType(LibType.DESIGN);
                return esVisualModelVo;
            }
        }else {
            BeanUtils.copyProperties(visualModelPrivateById,esVisualModelVo);
            esVisualModelVo.setLibType(LibType.PRIVATE);
            Long linkPublishedId = visualModelPrivateById.getLinkPublishedId();
            ESVisualModel designVisualModel = iVisualModelApiSvc.queryVisualModelById(1L, linkPublishedId);
            if(designVisualModel!=null){
                esVisualModelVo.setOldVersion(designVisualModel.getPublishVersion().equals(visualModelPrivateById.getPublishVersion())?Boolean.FALSE:Boolean.TRUE);
            }
            return esVisualModelVo;
        }
    }

    public void delPrivateVisualModel(Long visualModelId) {
        //逻辑删除
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("id", visualModelId));
        visualModelPrivateSvc.updateByQuery(boolQueryBuilder,"ctx._source.status=0",true);
    }

    public void delDesignVisualModel(Long visualModelId) {
        visualModelSvc.deleteById(visualModelId);
        //同时将所有私有模型的版本号重置成0
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("linkPublishedId", visualModelId));
        visualModelPrivateSvc.updateByQuery(boolQueryBuilder,"ctx._source.publishVersion=0",true);
        //刷新ci间关系
        iVisualModelApiSvc.flushAllEnableVisualModelCiRlt();
    }

    public ESVisualModel convertVisualModelStandard(Long id, Boolean groupStandard) {
        ESVisualModel visualModel = visualModelSvc.getById(id);
        if(DEFAULT_DOMAIN_ID.equals(visualModel.getDomainId())){
            visualModel.setGroupStandard(groupStandard);
            visualModelSvc.saveOrUpdate(visualModel);
        }else {
            throw new BinaryException("非集团元模型不可转换");
        }
        return visualModel;
    }

    public String createVisualFromStandard(Long id) {
        // 1. 校验并获取标准元模型
        ESVisualModel standardModel = getAndValidateStandardModel(id);
        Long targetDomainId = SysUtil.getCurrentUserInfo().getDomainId();
        // 2. 同步CI分类并获取ID映射
        Map<Long, Long> ciIdMap = syncCiClassesAndGetIdMap(standardModel, targetDomainId);
        // 3. 同步关系分类并获取ID映射
        Map<Long, Long> rltIdMap = syncRltClassesAndGetIdMap(standardModel, targetDomainId);
        // 4. 使用新的ID映射转换元模型JSON
        return transformJsonWithNewIds(standardModel, ciIdMap, rltIdMap);
    }

    /**
     * 校验并获取集团标准元模型.
     *
     * @param modelId 元模型ID
     * @return ESVisualModel 集团标准元模型
     * @throws BinaryException 如果不是集团标准元模型
     */
    private ESVisualModel getAndValidateStandardModel(Long modelId) {
        ESVisualModel visualModel = visualModelSvc.getById(modelId);
        if (visualModel == null || !DEFAULT_DOMAIN_ID.equals(visualModel.getDomainId()) || !Boolean.TRUE.equals(visualModel.getGroupStandard())) {
            throw new BinaryException("当前元模型非集团标准元模型");
        }
        return visualModel;
    }

    /**
     * 同步CI分类，处理冲突并返回新旧ID映射.
     *
     * @param standardModel 标准元模型
     * @param targetDomainId 目标租户ID
     * @return Map<Long, Long> 集团与当前租户的CI分类ID映射
     */
    private Map<Long, Long> syncCiClassesAndGetIdMap(ESVisualModel standardModel, Long targetDomainId) {
        Set<Long> ciClassIds = VisualModelUtils.getCiClassIds(standardModel);
        if (CollectionUtils.isEmpty(ciClassIds)) {
            return new HashMap<>();
        }
        return syncCiClassesWithDependencies(ciClassIds, targetDomainId);
    }

    /**
     * 同步CI分类及其所有依赖（父分类、目录、数据字典、关联CI），处理冲突并返回新旧ID映射.
     *
     * @param ciClassIds     需要同步的集团CI分类ID集合
     * @param targetDomainId 目标租户ID
     * @return Map<Long, Long> 集团与当前租户的CI分类ID映射
     */
    private Map<Long, Long> syncCiClassesWithDependencies(Set<Long> ciClassIds, Long targetDomainId) {
        // 准备两个Map：一个用于记录新旧ID映射，另一个用于存储需要新建的CI
        Map<Long, Long> idMap = new HashMap<>();
        List<CcCiClassInfo> classesToCreate = new ArrayList<>();

        //新旧属性idMap
        Map<Long, Long> attrIdMap = new HashMap<>();

        // 设置上下文到集团域，并确保操作结束后恢复
        DomainContext.setDomainContextValue(DomainContextValue.builder()
                .domainId(DEFAULT_DOMAIN_ID).domainReqType(DomainReqType.MANAGE).saas(Boolean.TRUE).build());
        try {
            // 1. 获取标准模型中所有CI分类的ID，并查询其完整信息
            if (CollectionUtils.isEmpty(ciClassIds)) {
                return idMap;
            }
            CCcCiClass groupCdt = new CCcCiClass();
            groupCdt.setIds(ciClassIds.toArray(new Long[0]));
            List<CcCiClassInfo> groupCiClassInfos = iciClassSvc.queryCiClassInfoList(DEFAULT_DOMAIN_ID, groupCdt, null, Boolean.FALSE);

            // 新增逻辑：提取所有关联的CI Class ID，确保它们也被同步
            Set<Long> relatedCiClassIds = new HashSet<>();
            if(!CollectionUtils.isEmpty(groupCiClassInfos)){
                for (CcCiClassInfo classInfo : groupCiClassInfos) {
                    for (CcCiAttrDef attrDef : classInfo.getAttrDefs()) {
                        if (attrDef.getProType() != null && attrDef.getProType() == 18 && attrDef.getProDropSourceDef() != null) {
                            try {
                                Long relatedId = Long.valueOf(String.valueOf(attrDef.getProDropSourceDef()));
                                // 如果这个关联ID不在我们已有的待处理列表中，则添加
                                if (!ciClassIds.contains(relatedId)) {
                                    relatedCiClassIds.add(relatedId);
                                }
                            } catch (Exception e) {
                                log.warn("处理关联CI的proDropSourceDef时发生异常: {}", e.getMessage());
                            }
                        }else if(attrDef.getProType() != null && attrDef.getProType() == ESPropertyType.EXTERNAL_ATTR.getValue() && attrDef.getProDropSourceDef() != null){
                            //关联属性
                            String proDropSourceDef = attrDef.getProDropSourceDef();
                            String[] split = proDropSourceDef.split(":");
                            Long relatedId = Long.valueOf(split[0].substring(1));
                            if (!ciClassIds.contains(relatedId)) {
                                relatedCiClassIds.add(relatedId);
                            }
                        }
                    }
                }
            }

            // 如果发现了新的关联CI，查询它们的详细信息并加入主列表
            if (!CollectionUtils.isEmpty(relatedCiClassIds)) {
                CCcCiClass relatedCdt = new CCcCiClass();
                relatedCdt.setIds(relatedCiClassIds.toArray(new Long[0]));
                List<CcCiClassInfo> relatedCiClassInfos = iciClassSvc.queryCiClassInfoList(DEFAULT_DOMAIN_ID, relatedCdt, null, Boolean.FALSE);
                // 将新查询到的CI信息合并到主列表中
                groupCiClassInfos.addAll(relatedCiClassInfos);
                // 同样更新ID集合，以便后续的父类查找也能覆盖到这些新加入的CI
                ciClassIds.addAll(relatedCiClassIds);
            }

            // 递归获取所有父分类，并将所有分类信息放入一个Map中以去重
            Map<Long, CcCiClassInfo> allGroupCiClassInfosMap = groupCiClassInfos.stream()
                    .collect(Collectors.toMap(info -> info.getCiClass().getId(), info -> info, (v1, v2) -> v2));
            java.util.Queue<CcCiClassInfo> queue = new java.util.LinkedList<>(groupCiClassInfos);
            Set<Long> processedIds = new HashSet<>(ciClassIds); // 用于跟踪已处理或已在队列中的ID，避免重复查询

            while (!queue.isEmpty()) {
                CcCiClassInfo current = queue.poll();
                CcCiClass ciClass = current.getCiClass();
                Long parentId = ciClass.getParentId();

                // 如果存在父ID，且这个父ID尚未处理过
                if (parentId != null && parentId != 0L && !processedIds.contains(parentId)) {
                    processedIds.add(parentId); // 标记为已处理

                    // 查询父分类的详细信息
                    CCcCiClass parentCdt = new CCcCiClass();
                    parentCdt.setIds(new Long[]{parentId});
                    List<CcCiClassInfo> parentInfos = iciClassSvc.queryCiClassInfoList(DEFAULT_DOMAIN_ID, parentCdt, null, Boolean.FALSE);

                    if (!CollectionUtils.isEmpty(parentInfos)) {
                        CcCiClassInfo parentInfo = parentInfos.get(0);
                        // 如果Map中还没有这个父分类，则加入Map并放入队列等待继续向上查找
                        if (!allGroupCiClassInfosMap.containsKey(parentInfo.getCiClass().getId())) {
                            allGroupCiClassInfosMap.put(parentInfo.getCiClass().getId(), parentInfo);
                            queue.add(parentInfo);
                        }
                    }
                }
            }
            // 使用包含所有父分类的完整列表进行后续操作
            groupCiClassInfos = new ArrayList<>(allGroupCiClassInfosMap.values());

            Map<String, CcCiClassInfo> groupCiClassMap = groupCiClassInfos.stream()
                    .collect(Collectors.toMap(info -> info.getCiClass().getClassCode(), info -> info, (v1, v2) -> v2));

            // 2. 获取当前租户的所有CI分类
            CCcCiClass targetCdt = new CCcCiClass();
            targetCdt.setDomainId(targetDomainId);
            List<CcCiClassInfo> targetCiClassInfos = iciClassSvc.queryClassByCdt(targetCdt);
            Map<String, CcCiClassInfo> targetCiClassMap = targetCiClassInfos.stream()
                    .collect(Collectors.toMap(info -> info.getCiClass().getClassCode(), info -> info, (v1, v2) -> v2));

            // 3. 检查并处理命名冲突
            Set<String> intersectionCodes = groupCiClassMap.keySet().stream()
                    .filter(targetCiClassMap::containsKey).collect(Collectors.toSet());
            Set<String> conflictCodes = new HashSet<>();
            for (String code : intersectionCodes) {
                CcCiClassInfo targetClass = targetCiClassMap.get(code);
                if (!Boolean.TRUE.equals(targetClass.getCiClass().getFromStandard())) {
                    conflictCodes.add(code);
                } else {
                    idMap.put(groupCiClassMap.get(code).getCiClass().getId(), targetClass.getCiClass().getId());

                    List<CcCiAttrDef> attrDefs = targetClass.getAttrDefs();
                    for (CcCiAttrDef attrDef : attrDefs) {
                        if(attrDef.getFieldRootId()!=null){
                            attrIdMap.put(attrDef.getFieldRootId(), attrDef.getId());
                        }
                    }

                }
            }
            if (!CollectionUtils.isEmpty(conflictCodes)) {
                throw new ClassConflictException(org.apache.commons.lang3.StringUtils.join(conflictCodes, ","));
            }

            // 4. 找出需要新建的CI分类
            Set<String> missingCodes = groupCiClassMap.keySet().stream()
                    .filter(code -> !targetCiClassMap.containsKey(code)).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(missingCodes)) {
                return idMap;
            }

            for (String code : missingCodes) {
                classesToCreate.add(groupCiClassMap.get(code));
            }

            // 5. 同步依赖的目录和数据字典，并获取ID映射
            Map<Long, Long> dirIdMap = syncAndMapDirs(classesToCreate, targetDomainId);
            Map<Long, Long> dictIdMap = syncAndMapDicts(classesToCreate, targetDomainId);

            // 预先为所有需要新建的CI分类生成新ID，并存入idMap
            for (CcCiClassInfo classInfo : classesToCreate) {
                idMap.putIfAbsent(classInfo.getCiClass().getId(), ESUtil.getUUID());
                List<CcCiAttrDef> attrDefs = classInfo.getAttrDefs();
                for (CcCiAttrDef attrDef : attrDefs) {
                    attrIdMap.putIfAbsent(attrDef.getId(), ESUtil.getUUID());
                }
            }

            // 6. 准备批量创建CI
            List<CcCiClassInfo> finalClassesToCreate = new ArrayList<>();
            for (CcCiClassInfo classInfo : classesToCreate) {
                 long oldClassId = classInfo.getCiClass().getId();
                 long newClassId = idMap.get(oldClassId);

                 // 替换目录ID和数据字典ID
                 CcCiClass ciClass = classInfo.getCiClass();
                 ciClass.setDirId(dirIdMap.get(ciClass.getDirId()));
                 ciClass.setDomainId(targetDomainId);
                 ciClass.setFromStandard(Boolean.TRUE);
                 ciClass.setRootId(oldClassId);
                 ciClass.setId(newClassId);

                 // 设置新的父ID
                 if (ciClass.getParentId() != null && ciClass.getParentId() != 0L) {
                     Long newParentId = idMap.get(ciClass.getParentId());
                     ciClass.setParentId(newParentId);
                 }

                 for(CcCiAttrDef attrDef : classInfo.getAttrDefs()){
                     Long domainClassFileId = attrDef.getId();
                     attrDef.setId(attrIdMap.get(domainClassFileId));
                     attrDef.setDomainId(targetDomainId);
                     attrDef.setClassId(newClassId);
                     attrDef.setFieldRootId(domainClassFileId);
                     if(ESPropertyType.DICT.getValue() == attrDef.getProType()){
                         Long dictClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(attrDef.getProDropSourceDef().trim());
                         Long targetDictId = dictIdMap.get(dictClassId);
                         String[] split = attrDef.getProDropSourceDef().split(":");
                         attrDef.setProDropSourceDef("{"+targetDictId + ":" + split[1]);
                     }
                    // 如果是关联关系类型(18)，其proDropSourceDef存放的是ciClass的ID，需要进行同步转换
                    if (ESPropertyType.LINK_CI.getValue() == attrDef.getProType()) {
                        try {
                            Long oldCiClassId = Long.valueOf(String.valueOf(attrDef.getProDropSourceDef()));
                            if (idMap.containsKey(oldCiClassId)) {
                                Long newCiClassId = idMap.get(oldCiClassId);
                                attrDef.setProDropSourceDef(String.valueOf(newCiClassId));
                            }
                        }catch (Exception e){
                            log.warn("proDropSourceDef转换异常 {}",e.getMessage());
                        }
                    }
                    //如果是关联属性也需要替换
                     if (ESPropertyType.EXTERNAL_ATTR.getValue() == attrDef.getProType()) {
                         try {
                             String proDropSourceDef = attrDef.getProDropSourceDef();
                             String[] split = proDropSourceDef.split(":");
                             Long oldCiClassId = Long.valueOf(split[0].substring(1));
                             if (idMap.containsKey(oldCiClassId)) {
                                 Long newCiClassId = idMap.get(oldCiClassId);
                                 //split[1]也需要用新的id替换
                                 Long oldAttrId = Long.valueOf(split[1].substring(0, split[1].length() - 1));
                                 Long newAttrId = attrIdMap.get(oldAttrId);
                                 attrDef.setProDropSourceDef("{" + newCiClassId + ":" + newAttrId+"}");
                             }
                         } catch (Exception e) {
                             log.warn("proDropSourceDef转换异常 {}", e.getMessage());
                         }
                     }
                 }
                finalClassesToCreate.add(classInfo);
            }
             // 7. 切换回当前租户上下文并批量保存
            DomainContext.setDomainContextValue(DomainContextValue.builder()
                .domainId(targetDomainId).domainReqType(DomainReqType.FRONT).saas(Boolean.TRUE).build());

            if(!CollectionUtils.isEmpty(finalClassesToCreate)){
                log.info("需要保存的分类数量：{}", finalClassesToCreate.size());
                //子父分类分开保存，先保存父分类，保证分类是存在的
                List<CcCiClassInfo> notSubClass = new ArrayList<>();
                List<CcCiClassInfo> subClass = new ArrayList<>();
                for (CcCiClassInfo ccCiClassInfo : finalClassesToCreate) {
                    if(ccCiClassInfo.getCiClass().getParentId() == 0L){
                        notSubClass.add(ccCiClassInfo);
                    }else {
                        subClass.add(ccCiClassInfo);
                    }
                }
                if(!CollectionUtils.isEmpty(notSubClass)){
                    iciClassSvc.saveOrUpdateBatchNoCheck(targetDomainId, notSubClass);
                }
                if(!CollectionUtils.isEmpty(subClass)){
                    iciClassSvc.saveOrUpdateBatchNoCheck(targetDomainId, subClass);
                }
            }

        } finally {
            // 确保上下文被重置
            DomainContext.setDomainContextValue(DomainContextValue.builder()
                    .domainId(targetDomainId).domainReqType(DomainReqType.FRONT).saas(Boolean.TRUE).build());
        }
        return idMap;
    }

    /**
     * 同步关系分类，并返回新旧ID映射.
     *
     * @param standardModel  标准元模型
     * @param targetDomainId 目标租户ID
     * @return Map<Long, Long> 集团与当前租户的关系分类ID映射
     */

    /**
     * 同步并映射目录.
     */
    private Map<Long, Long> syncAndMapDirs(List<CcCiClassInfo> classesToCreate, Long targetDomainId) {
        Map<Long, Long> dirIdMap = new HashMap<>();
        Set<Long> dirIds = classesToCreate.stream().map(info -> info.getCiClass().getDirId()).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(dirIds)) return dirIdMap;

        CCcCiClassDir groupDirCdt = new CCcCiClassDir();
        groupDirCdt.setIds(dirIds.toArray(new Long[0]));
        List<CcCiClassDir> groupDirs = iDirSvc.queryDirList(groupDirCdt, null, Boolean.FALSE);

        CCcCiClassDir targetDirCdt = new CCcCiClassDir();
        targetDirCdt.setDomainId(targetDomainId);
        List<CcCiClassDir> targetDirs = iDirSvc.queryDirList(targetDirCdt, null, Boolean.FALSE);
        Map<String, CcCiClassDir> targetDirMap = targetDirs.stream().collect(Collectors.toMap(CcCiClassDir::getDirName, d -> d, (v1, v2) -> v2));

        for (CcCiClassDir groupDir : groupDirs) {
            CcCiClassDir targetDir = targetDirMap.get(groupDir.getDirName());
            if (targetDir == null) {
                long oldDirId = groupDir.getId();
                groupDir.setId(null);
                groupDir.setDomainId(targetDomainId);
                long newDirId = iDirSvc.saveOrUpdate(groupDir);
                dirIdMap.put(oldDirId, newDirId);
            } else {
                dirIdMap.put(groupDir.getId(), targetDir.getId());
            }
        }
        return dirIdMap;
    }

    /**
     * 同步并映射数据字典.
     */
    private Map<Long, Long> syncAndMapDicts(List<CcCiClassInfo> classesToCreate, Long targetDomainId) {
         Map<Long, Long> dictIdMap = new HashMap<>();
         // 提取所有数据字典类型的属性
         Set<Long> groupDictIds = new HashSet<>();
         for(CcCiClassInfo classInfo : classesToCreate){
             for(CcCiAttrDef attrDef : classInfo.getAttrDefs()){
                 if(ESPropertyType.DICT.getValue() == attrDef.getProType()){
                     groupDictIds.add(CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(attrDef.getProDropSourceDef().trim()));
                 }
             }
         }
         if(CollectionUtils.isEmpty(groupDictIds)) return dictIdMap;

        List<DictionaryInfoDto> groupDicts = dictionaryApiSvc.getDictionaryClassList(DEFAULT_DOMAIN_ID).stream()
                .filter(d -> groupDictIds.contains(d.getId())).collect(Collectors.toList());
        List<DictionaryInfoDto> targetDicts = dictionaryApiSvc.getDictionaryClassList(targetDomainId);
        Map<String, DictionaryInfoDto> targetDictMap = targetDicts.stream().collect(Collectors.toMap(DictionaryInfoDto::getDictCode, d -> d, (v1, v2) -> v2));

        for (DictionaryInfoDto groupDict : groupDicts) {
            DictionaryInfoDto targetDict = targetDictMap.get(groupDict.getDictCode());
            if (targetDict == null) {
                long oldDictId = groupDict.getId();
                groupDict.setId(ESUtil.getUUID());
                groupDict.setDomainId(targetDomainId);
                dictionaryApiSvc.saveDictionaryClassInfo(groupDict);
                dictIdMap.put(oldDictId, groupDict.getId());
            } else {
                dictIdMap.put(groupDict.getId(), targetDict.getId());
            }
        }
        return dictIdMap;
    }

    /**
     * 同步关系分类，并返回新旧ID映射.
     *
     * @param standardModel  标准元模型
     * @param targetDomainId 目标租户ID
     * @return Map<Long, Long> 集团与当前租户的关系分类ID映射
     */
    private Map<Long, Long> syncRltClassesAndGetIdMap(ESVisualModel standardModel, Long targetDomainId) {
        Map<Long, Long> idMap = new HashMap<>();
        DomainContext.setDomainContextValue(DomainContextValue.builder()
                .domainId(DEFAULT_DOMAIN_ID).domainReqType(DomainReqType.MANAGE).saas(Boolean.TRUE).build());
        try {
            // 1. 获取标准模型中的关系及其信息
            List<DiagramNodeLinkInfo> rltLinks = VisualModelUtils.getRltClassIds(standardModel);
            Set<Long> rltClassIds = rltLinks.stream().map(DiagramNodeLinkInfo::getLinkId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(rltClassIds)) return idMap;

            CCcCiClass groupCdt = new CCcCiClass();
            groupCdt.setIds(rltClassIds.toArray(new Long[0]));
            List<CcCiClassInfo> groupRltInfos = iRltClassSvc.getRltClassByCdt(groupCdt);
            Map<String, CcCiClassInfo> groupRltMap = groupRltInfos.stream().collect(Collectors.toMap(info -> info.getCiClass().getClassCode(), info -> info));

            // 2. 获取当前租户的所有关系
            List<CcCiClassInfo> targetRltInfos = iRltClassSvc.queryAllClasses(targetDomainId);
            Map<String, CcCiClassInfo> targetRltMap = targetRltInfos.stream().collect(Collectors.toMap(info -> info.getCiClass().getClassCode(), info -> info));

            // 3. 处理已存在的关系，建立ID映射
            Set<String> intersectionCodes = Sets.intersection(groupRltMap.keySet(), targetRltMap.keySet());
            for (String code : intersectionCodes) {
                idMap.put(groupRltMap.get(code).getCiClass().getId(), targetRltMap.get(code).getCiClass().getId());
            }

            // 4. 找出并创建缺失的关系
            Set<String> differenceCodes = Sets.difference(groupRltMap.keySet(), targetRltMap.keySet());
            List<ESCIClassInfo> rltsToCreate = new ArrayList<>();
            if (!CollectionUtils.isEmpty(differenceCodes)) {
                for (String code : differenceCodes) {
                    CcCiClassInfo groupRlt = groupRltMap.get(code);
                    long newRltId = ESUtil.getUUID();
                    Long groupRltId = groupRlt.getCiClass().getId();
                    idMap.put(groupRltId, newRltId);

                    ESCIClassInfo esciClassInfo = new ESCIClassInfo();
                    BeanUtils.copyProperties(groupRlt.getCiClass(), esciClassInfo);
                    List<ESCIAttrDefInfo> esAttrDefs = groupRlt.getAttrDefs().stream().map(attr -> {
                        ESCIAttrDefInfo esAttr = new ESCIAttrDefInfo();
                        BeanUtils.copyProperties(attr, esAttr);
                        attr.setId(ESUtil.getUUID());
                        attr.setDomainId(targetDomainId);
                        attr.setClassId(newRltId);
                        return esAttr;
                    }).collect(Collectors.toList());
                    esciClassInfo.setRootId(groupRltId);
                    esciClassInfo.setFromStandard(Boolean.TRUE);
                    esciClassInfo.setAttrDefs(esAttrDefs);
                    esciClassInfo.setDomainId(targetDomainId);
                    esciClassInfo.setId(newRltId);
                    rltsToCreate.add(esciClassInfo);
                }
            }
             // 5. 切换上下文并批量保存
            DomainContext.setDomainContextValue(DomainContextValue.builder()
                .domainId(targetDomainId).domainReqType(DomainReqType.FRONT).saas(Boolean.TRUE).build());
                
            if (!CollectionUtils.isEmpty(rltsToCreate)) {
                log.info("需要保存的关系数量：{}", rltsToCreate.size());
                esRltClassSvc.saveOrUpdateBatch(rltsToCreate);
            }
        } finally {
            DomainContext.setDomainContextValue(DomainContextValue.builder()
                    .domainId(targetDomainId).domainReqType(DomainReqType.FRONT).saas(Boolean.TRUE).build());
        }
        return idMap;
    }

    /**
     * 使用新的ID映射转换元模型JSON.
     *
     * @param visualModel           元模型
     * @param classIdMap            CI分类ID映射
     * @param rltClassIdMap         关系分类ID映射
     * @return String 转换后的JSON字符串
     */
    private String transformJsonWithNewIds(ESVisualModel visualModel, Map<Long, Long> classIdMap, Map<Long, Long> rltClassIdMap) {
        String json = visualModel.getJson();

        // 检查JSON是否为空
        if (BinaryUtils.isEmpty(json)) {
            return "";
        }
        try {
            // 解析JSON为数组
            JSONArray jsonArray = JSONArray.parseArray(json);
            if (BinaryUtils.isEmpty(jsonArray)) {
                return "";
            }
            // 遍历每个视图对象
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject viewObject = jsonArray.getJSONObject(i);
                if (viewObject == null) {
                    continue;
                }
                // 处理linkDataArray中的classId替换
                JSONArray linkDataArray = viewObject.getJSONArray("linkDataArray");
                if (!BinaryUtils.isEmpty(linkDataArray)) {
                    for (int j = 0; j < linkDataArray.size(); j++) {
                        JSONObject linkData = linkDataArray.getJSONObject(j);
                        if (linkData != null && linkData.containsKey("classId")) {
                            Long originalClassId = linkData.getLong("classId");
                            if (originalClassId != null && rltClassIdMap.containsKey(originalClassId)) {
                                Long newClassId = rltClassIdMap.get(originalClassId);
                                linkData.put("classId", newClassId);
                            }
                        }
                    }
                }
                // 处理nodeDataArray中的classId替换
                JSONArray nodeDataArray = viewObject.getJSONArray("nodeDataArray");
                if (!BinaryUtils.isEmpty(nodeDataArray)) {
                    for (int k = 0; k < nodeDataArray.size(); k++) {
                        JSONObject nodeData = nodeDataArray.getJSONObject(k);
                        if (nodeData != null && nodeData.containsKey("classId")) {
                            Long originalClassId = nodeData.getLong("classId");
                            if (originalClassId != null && classIdMap.containsKey(originalClassId)) {
                                Long newClassId = classIdMap.get(originalClassId);
                                nodeData.put("classId", newClassId);
                            }
                        }
                    }
                }
            }
            // 更新visualModel的JSON
            return jsonArray.toJSONString();
        } catch (Exception e) {
            log.error("转换元模型到当前域失败", e);
            throw new BinaryException("转换元模型到当前域失败: " + e.getMessage());
        }
    }

    public List<ESVisualModelVo> getReferenceVisualList() {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("enable",false));
        List<ESVisualModel> listByQueryScroll = visualModelSvc.getListByQueryScroll(boolQueryBuilder);

        if(!DEFAULT_DOMAIN_ID.equals(SysUtil.getCurrentUserInfo().getDomainId())){
            //查询集团租户的标准元模型
            DomainContext.setDomainContextValue(DomainContextValue.builder()
                    .domainId(DEFAULT_DOMAIN_ID)
                    .domainReqType(DomainReqType.MANAGE)
                    .saas(Boolean.TRUE)
                    .build());
            boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", DEFAULT_DOMAIN_ID));
            boolQueryBuilder.filter(QueryBuilders.termQuery("groupStandard", Boolean.TRUE));
            boolQueryBuilder.filter(QueryBuilders.termQuery("enable", Boolean.TRUE));
            List<ESVisualModel> standardVisualModelList = visualModelSvc.getListByQueryScroll(boolQueryBuilder);
            listByQueryScroll.addAll(standardVisualModelList);
        }
        List<ESVisualModelVo> esVisualModelVos = new ArrayList<>();
        for (ESVisualModel model : listByQueryScroll) {
            ESVisualModelVo esVisualModelVo = new ESVisualModelVo();
            BeanUtils.copyProperties(model,esVisualModelVo);
            esVisualModelVo.setJson("");
            esVisualModelVo.setLibType(LibType.DESIGN);
            esVisualModelVos.add(esVisualModelVo);
        }
        return esVisualModelVos;
    }

    public Long convertVisualModelToExample(Long id) {
        //1、根据id查询当前发布的元模型
        ESVisualModel visualModel = visualModelSvc.getById(id);
        visualModel.setEnable(Boolean.FALSE);
        visualModel.setGroupStandard(Boolean.FALSE);
        visualModel.setId(ESUtil.getUUID());
        return visualModelSvc.saveOrUpdate(visualModel);
    }


    public ESVisualModel updatePrivateVisualModel(ESVisualModel model) {

        ESVisualModel privateVisualModel = visualModelPrivateSvc.getById(model.getId());
        ESVisualModel designVisualModel = visualModelSvc.getById(privateVisualModel.getLinkPublishedId());

        if (designVisualModel != null && !designVisualModel.getPublishVersion().equals(privateVisualModel.getPublishVersion())) {
            privateVisualModel.setPublishVersion(designVisualModel.getPublishVersion());
            privateVisualModel.setName(designVisualModel.getName());
            privateVisualModel.setJson(designVisualModel.getJson());
            privateVisualModel.setThumbnail(designVisualModel.getThumbnail());
            privateVisualModel.setEditRoleIds(designVisualModel.getEditRoleIds());
            privateVisualModel.setViewRoleIds(designVisualModel.getViewRoleIds());
            privateVisualModel.setSelectedShapeGroupIds(designVisualModel.getSelectedShapeGroupIds());
            visualModelPrivateSvc.saveOrUpdate(privateVisualModel);
        }
        return privateVisualModel;
    }

    public PorcessResponse approveVisualModel(VisualModelsDto dto) {
        //查询元模型审批角色下是否配置用户，没配置提示配置用户
        //1、查询元模型管理员角色是否存在
         SysRole visualModeRole = esRoleSvc.getByName(1L, "元模型管理员");;
        if (visualModeRole == null) {
            throw new BinaryException("请先配置元模型管理员角色");
        }
        //2、查询元模型管理员角色下是否配置用户
        List<SysUser> visualModeAdminUser = userSvc.getUserByRoleId(visualModeRole.getId());;
        if (CollectionUtils.isEmpty(visualModeAdminUser)) {
            throw new BinaryException("请先配置元模型管理员角色用户");
        }

        //元模型发布前检查
        ESVisualModel modelPrivate = visualModelPrivateSvc.getById(dto.getVisualModelId());
        if (modelPrivate == null) {
            throw new BinaryException("元模型不存在");
        }
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        //执行元模型发布审批
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setBusinessKey(dto.getVisualModelId().toString());
        processRequest.setProcessDefinitionKey(FlowableConstant.VISUAL_MODEL_PUBLISH_APPROVE);
        processRequest.setProcessInstanceName(modelPrivate.getName() + "元模型审批流程");
        processRequest.setOwner(loginCode);
        processRequest.setUserId(loginCode);
        HashMap<String, Object> routerVariables = new HashMap<>();
        routerVariables.put("publishDescription",dto.getPublishDescription());
        routerVariables.put("domainId", DomainContext.getDomainContextValue().getDomainId());
        processRequest.setRouterVariables(routerVariables);
        PorcessResponse porcessResponse = flowableFeign.startProcessBindAssignee(processRequest);
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setAction(FLOWACTION.ACCETP);
        taskRequest.setTaskId(porcessResponse.getTaskId());
        Map<String, Object> taskEchoVariables = new HashMap<>();
        taskEchoVariables.put("domainId", DomainContext.getDomainContextValue().getDomainId());
        taskRequest.setTaskEchoVariables(taskEchoVariables);
        flowableFeign.completeTask(taskRequest);
        //修改元模型状态
        modelPrivate.setApprove(1);
        visualModelPrivateSvc.saveOrUpdate(modelPrivate);
        return porcessResponse;
    }

    public TaskResponse completeTask(TaskRequest taskRequest) {
        TaskResponse taskInfoByTaskId = flowableFeign.getTaskInfoByTaskId(taskRequest.getTaskId());
        if (FLOWACTION.REJECT.equals(taskRequest.getAction())) {
            String businessKey = taskInfoByTaskId.getBusinessKey();
            ESVisualModel esVisualModel = visualModelPrivateSvc.getById(Long.valueOf(businessKey));
            esVisualModel.setApprove(2);
            visualModelPrivateSvc.saveOrUpdate(esVisualModel);
        } else if (FLOWACTION.ACCETP.equals(taskRequest.getAction()) && "rectification".equalsIgnoreCase(taskInfoByTaskId.getDescription())
                || (FLOWACTION.ACCEPT.equals(taskRequest.getAction()) && "rectification".equalsIgnoreCase(taskInfoByTaskId.getDescription()))) {
            //如果是提交人重新提交的话需要重新将状态改为审批中不可编辑
            String businessKey = taskInfoByTaskId.getBusinessKey();
            ESVisualModel esVisualModel = visualModelPrivateSvc.getById(Long.valueOf(businessKey));
            esVisualModel.setApprove(1);
            String publishDescription = taskRequest.getPublishDescription();
            if (!StringUtils.isBlank(publishDescription)) {
                HashMap<String, Object> routerVariables = new HashMap<>();
                routerVariables.put("publishDescription", taskRequest.getPublishDescription());
                taskRequest.setRouterVariables(routerVariables);
            }
            visualModelPrivateSvc.saveOrUpdate(esVisualModel);
        } else if (FLOWACTION.CANCEL.equals(taskRequest.getAction())) {
            String businessKey = taskInfoByTaskId.getBusinessKey();
            ESVisualModel esVisualModel = visualModelPrivateSvc.getById(Long.valueOf(businessKey));
            esVisualModel.setApprove(0);
            visualModelPrivateSvc.saveOrUpdate(esVisualModel);
        }

        Map<String, Object> taskEchoVariables = new HashMap<>();
        taskEchoVariables.put("domainId", DomainContext.getDomainContextValue().getDomainId());
        taskRequest.setTaskEchoVariables(taskEchoVariables);
        TaskResponse taskResponse = flowableFeign.completeTask(taskRequest);
        if (taskResponse.getProcessEnd() && FLOWACTION.ACCETP.equals(taskRequest.getAction())||
                taskResponse.getProcessEnd() && FLOWACTION.ACCEPT.equals(taskRequest.getAction())) {
            //流程审批通过就执行发布
            PorcessResponse processInstanceByProcessInstanceId = flowableFeign.getProcessInstanceByProcessInstanceId(taskInfoByTaskId.getProcessInstanceId());
            Map<String, Object> routerVariables = processInstanceByProcessInstanceId.getRouterVariables();
            Object o = routerVariables.get("publishDescription");
            String publishDescription = "";
            if (o != null) {
                publishDescription = o.toString();
            }
            publishVisualModel(Long.parseLong(taskInfoByTaskId.getBusinessKey()), publishDescription);
        }

        return taskResponse;
    }

    public List<ESVisualModelHistory> getVisualPublishHistory(String designVisualModelId) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("linkPublishedId", designVisualModelId));
        List<ESVisualModelHistory> listByQuery = esVisualModelHistorySvc.getSortListByQueryScroll(boolQueryBuilder,"publishVersion",false);
        //从listByQuery中获取creater字段的数据并放到set中，判断下creater为空就从modifier获取
        Set<String> createrSet = new HashSet<>();
        for (ESVisualModelHistory esVisualModelHistory : listByQuery) {
            if (StringUtils.isBlank(esVisualModelHistory.getCreator())) {
                createrSet.add(esVisualModelHistory.getModifier());
            } else {
                createrSet.add(esVisualModelHistory.getCreator());
            }
        }

        //使用userSvc获取所有用户信息
        CSysUser cSysUser = new CSysUser();
        cSysUser.setLoginCodes(createrSet.toArray(new String[0]));
        List<SysUser> sysUserByCdt = userSvc.getSysUserByCdt(cSysUser);
        Map<String, SysUser> sysUserMap = sysUserByCdt.stream().collect(Collectors.toMap(SysUser::getLoginCode, item -> item, (v1, v2) -> v2));
        for (ESVisualModelHistory esVisualModelHistory : listByQuery) {
            if (StringUtils.isBlank(esVisualModelHistory.getCreator())) {
                esVisualModelHistory.setPublishUserName(sysUserMap.get(esVisualModelHistory.getModifier()).getUserName());
            } else {
                esVisualModelHistory.setPublishUserName(sysUserMap.get(esVisualModelHistory.getCreator()).getUserName());
            }
        }

        return listByQuery;
    }

    /**
     * 查询元模型历史CI
     * @param visualModelId
     * @param classId
     * @return
     */
    public ClassInfoHistory getVisualModelHistoryCi(Long visualModelId, Long classId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("visualPublishVersionId", visualModelId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("classId", classId));
        ClassInfoHistory cInfoHistory = esClassInfoHistorySvc.selectOne(boolQueryBuilder);
        return cInfoHistory;
    }

    /**
     * 查询元模型历史关系信息
     * @param visualModeHistoryId
     * @param rltId
     * @return
     */
    public RltInfoHistory getVisualModelHistoryRlt(Long visualModeHistoryId, Long rltId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("visualPublishVersionId", visualModeHistoryId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("rltId", rltId));
        RltInfoHistory rltInfoHistory = esRltInfoHistorySvc.selectOne(boolQueryBuilder);
        return rltInfoHistory;
    }

    private void checkVisualModel(VisualModelsDto dto) {
        if (dto == null) {
            throw new ServiceException("导入元模型参数不能为空!");
        }
        if (StringUtils.isEmpty(dto.getJsonFileStr())) {
            throw new ServiceException("上传的json数据不能为空!");
        }
        if (dto.getVisualModelId() == null) {
            throw new ServiceException("元模型不能为空!");
        }
        if (dto.getSheetId() == null) {
            throw new ServiceException("sheetId不能为空!");
        }
    }


    public void delVMThumbnailBySheetId(Long id, Long sheetId) {
        if (BinaryUtils.isEmpty(sheetId)) {
            throw new ServiceException("sheetId不能为空!");
        }
        if (BinaryUtils.isEmpty(id)) {
            throw new ServiceException("元模型Id不能为空!");
        }
        iVisualModelApiSvc.delVMThumbnailBySheetId(id, sheetId);
    }


    public Map<Long, Long> refreshClassIdByVisId(Long visId) {
        Map<Long, Long> data = new HashMap<>();
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        // 根据visId查询元模型数据
        ESVisualModel visualModel = iVisualModelApiSvc.queryVisualModelById(currentUserInfo.getDomainId(), visId);
        if (BinaryUtils.isEmpty(visualModel)) {
            throw new BinaryException("元模型不存在");
        }
        // 查询所有class信息 code 和 id 组成 map
        CCcCiClass cdt = new CCcCiClass();
        cdt.setDomainId(currentUserInfo.getDomainId());
        List<CcCiClassInfo> ciClassList = iciClassSvc.queryClassByCdt(cdt);
        Map<String, Long> ciClassMap = ciClassList.stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getClassCode, CcCiClass::getId, (k1, k2) -> k2));

        List<CcCiClassInfo> rltClassList = iRltClassSvc.queryAllClasses(currentUserInfo.getDomainId());
        Map<String, Long> rltCodeMap = rltClassList.stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getClassCode, CcCiClass::getId, (k1, k2) -> k2));

        JSONArray jsonArray = JSON.parseArray(visualModel.getJson());
        if (!BinaryUtils.isEmpty(jsonArray)) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject json = jsonArray.getJSONObject(i);
                JSONArray nodeList = json.getJSONArray("nodeDataArray");
                if(BinaryUtils.isEmpty(nodeList)){
                    continue;
                }
                Map<Integer, Long> keyClassMap = replaceCiClassId(nodeList, ciClassMap, data);
                json.put("nodeDataArray", nodeList);
                JSONArray linkList = json.getJSONArray("linkDataArray");
                if(BinaryUtils.isEmpty(linkList)){
                    continue;
                }
                replaceRltClassId(linkList, rltCodeMap, keyClassMap, data);
                json.put("linkDataArray", linkList);
            }
        }
        visualModel.setJson(jsonArray.toJSONString());
        visualModelSvc.saveOrUpdate(visualModel);
        return data;
    }

    /**
     * 刷新元模型node节点分类id
     * @param nodeList 元模型节点集合
     * @param ciClassMap classCode->classId映射
     * @param data 刷新的classId映射
     * @return node节点key与匹配到的分类id映射
     */
    private Map<Integer, Long> replaceCiClassId(JSONArray nodeList, Map<String, Long> ciClassMap, Map<Long, Long> data){
        Map<Integer, Long> keyClassMap = new HashMap<>();
        for (int i = 0; i < nodeList.size(); i++) {
            JSONObject node = nodeList.getJSONObject(i);
            String classCode = node.getString("classCode");
            Long classId = node.getLong("classId");
            if (!BinaryUtils.isEmpty(classCode)) {
                // 刷新数据中的classId
                Long id = ciClassMap.get(classCode);
                if (!BinaryUtils.isEmpty(id)) {
                    data.put(classId, id);
                    node.put("classId", id);
                    keyClassMap.put(node.getInteger("key"), id);
                } else {
                    // json中的classId 在全局分类匹配不到 当前导入分类按照图标处理
                    node.remove("classId");
                }
            } else {
                log.info("vis_nodeData========" + node.toJSONString());
            }
        }
        return keyClassMap;
    }

    /**
     * 刷新元模型关系线分类id
     * @param linkList 关系线集合
     * @param rltCodeMap 关系分类code->分类id映射
     * @param keyClassMap node节点key与匹配到的分类id映射
     * @param data 刷新的classId映射
     */
    private void replaceRltClassId(JSONArray linkList, Map<String, Long> rltCodeMap, Map<Integer, Long> keyClassMap, Map<Long, Long> data){
        for (int i = 0; i < linkList.size(); i++) {
            JSONObject link = linkList.getJSONObject(i);
            // 存量json文件可能缺少关系线的code信息 如果缺失取label 如果都缺失 即为普通线段
            String classCode = !BinaryUtils.isEmpty(link.getString("classCode")) ? link.getString("classCode") : link.getString("label");
            Integer from = link.getInteger("from");
            Integer to = link.getInteger("to");
            if(BinaryUtils.isEmpty(classCode)){
                log.info("vis_nodeData========" + link.toJSONString());
                continue;
            }
            Long id = rltCodeMap.get(classCode);
            if (keyClassMap.get(from) == null || keyClassMap.get(to) == null || BinaryUtils.isEmpty(id)) {
                // json中的classId 在全局分类匹配不到 当前导入分类按照图标处理
                link.remove("classId");
            }else{
                // 刷新数据中的classId
                data.put(link.getLong("classId"), id);
                link.put("classId", id);
                // 同时更新一下classCode/label信息
                link.put("classCode", classCode);
                link.put("label", classCode);
            }
        }
    }

    public Boolean isEmploy(Long visId) {
        Long id = SysUtil.getCurrentUserInfo().getId();
        Object userId = iCacheService.getCache(VISUAL_MODEL_KEY + visId);
        // 只要当前用户没在登录状态都是false
        if (BinaryUtils.isEmpty(userId)) {
            // redis中无信息 当前用户可以编辑
            return false;
        }
        if (Objects.equals(id, Long.valueOf(userId.toString()))) {
            // 当前登录用户是编辑用户
            return true;
        }
        return false;
    }

    /**
     * 保存元模型3D全景配置
     * @param modelPanorama3D 元模型3D全景配置
     * @return 保存结果ID
     */
    public Long saveModelPanorama3D(ModelPanorama3D modelPanorama3D) {
        if (modelPanorama3D == null) {
            throw new ServiceException("元模型3D全景配置不能为空!");
        }
        if (modelPanorama3D.getModelId() == null) {
            throw new ServiceException("元模型ID不能为空!");
        }
        return modelPanorama3DSvc.saveModelPanorama3D(modelPanorama3D);
    }

    /**
     * 根据元模型ID查询3D全景配置
     * @param modelId 元模型ID
     * @return 元模型3D全景配置
     */
    public ModelPanorama3D queryModelPanorama3DByModelId(Long modelId) {
        if (modelId == null) {
            throw new ServiceException("元模型ID不能为空!");
        }
        return modelPanorama3DSvc.queryByModelId(modelId);
    }


}
