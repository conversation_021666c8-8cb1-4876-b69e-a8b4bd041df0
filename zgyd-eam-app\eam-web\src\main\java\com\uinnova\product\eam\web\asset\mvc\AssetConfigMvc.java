package com.uinnova.product.eam.web.asset.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.CcCiClassInfoConfVO;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.web.asset.bean.AssetDetailAttrConfVO;
import com.uinnova.product.eam.web.asset.bean.AssetLisConfSearchParam;
import com.uinnova.product.eam.web.asset.bean.AssetListAttrConfVO;
import com.uinnova.product.eam.web.asset.peer.AssetConfigPeer;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.dao.saas.constant.DomainReqType;
import com.uino.dao.saas.context.DomainContext;
import com.uino.dao.saas.context.DomainContextValue;
import com.uino.web.auth.VerifyAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 资产配置控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/assetConf")
@Slf4j
public class AssetConfigMvc {

    /**
     * 1. 应用广场配置资产属性配置
     * 2. 卡片、列表展示属性配置
     * 3. 详情展示属性配置信息
     */
    @Resource
    AssetConfigPeer assetConfigPeer;

    @Resource
    private BmConfigSvc bmConfigSvc;

    @Autowired
    private VerifyAuthUtil verifyAuth;
    @Value("${uino.eam.saas:false}")
    private Boolean saas;

    /**
     * 保存列表配置
     *
     * @param listAttrVO
     * @return
     */
    @PostMapping("saveOrUpdateListAttr")
    public RemoteResult saveOrUpdateListAttr(@RequestBody AssetListAttrConfVO listAttrVO) {
        Long id = assetConfigPeer.saveOrUpdateListAttr(listAttrVO);
        return new RemoteResult(id);
    }

    @GetMapping("getListAttr")
    public RemoteResult getListAttr(@RequestParam Long appSquareConfId, @RequestParam Integer type) {
        AssetListAttrConfVO vo = assetConfigPeer.getListAttr(appSquareConfId, type);
        if (BinaryUtils.isEmpty(vo)) {
            vo = new AssetListAttrConfVO();
        }
        return new RemoteResult(vo);
    }

    @GetMapping("getListAttrClassInfo")
    public RemoteResult getListAttrClassInfo(@RequestParam Long appSquareConfId, @RequestParam Integer type) {
        CcCiClassInfoConfVO vo = assetConfigPeer.getListAttrClassInfo(appSquareConfId, type);
        return new RemoteResult(vo);
    }

    @GetMapping("deleteListAttrById")
    public RemoteResult deleteListAttrById(@RequestParam Long id) {
        Integer num = assetConfigPeer.deleteListAttrById(id);
        return new RemoteResult(num);
    }

    @PostMapping("saveOrUpdateDetailAttr")
    public RemoteResult saveOrUpdateDetailAttr(@RequestBody AssetDetailAttrConfVO detailAttrConfVO) {
        Long num = assetConfigPeer.saveOrUpdateDetailAttr(detailAttrConfVO);
        return new RemoteResult(num);
    }

    @GetMapping("getDetailAttr")
    public RemoteResult getDetailAttr(@RequestParam Long appSquareConfId) {
        AssetDetailAttrConfVO result = assetConfigPeer.getDetailAttr(appSquareConfId);
        return new RemoteResult(result);
    }

    @GetMapping("getClassInfoDetailAttr")
    public RemoteResult getClassInfoDetailAttr(@RequestParam Long appSquareConfId) {
        CcCiClassInfoConfVO result = assetConfigPeer.getClassInfoDetailAttr(appSquareConfId);
        return new RemoteResult(result);
    }

    @GetMapping("deleteDetailAttrById")
    public RemoteResult deleteDetailAttrById(@RequestParam Long id) {
        Integer num = assetConfigPeer.deleteDetailAttrById(id);
        return new RemoteResult(num);
    }

    @RequestMapping("/queryAssetCiInfoPage")
    @ModDesc(desc = "分页查询对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CCcCi.class, rDesc = "对象数据", rType = Page.class)
    public RemoteResult queryCiInfoPage(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                        @RequestBody AssetLisConfSearchParam param) {
        Page<ESCIInfo> pageResult = assetConfigPeer.queryCiInfoPage(libType, param);
        return new RemoteResult(pageResult);

    }


    @GetMapping("assetManageShowType")
    public RemoteResult assetManageShowType(@RequestParam String key) {
        BinaryUtils.checkEmpty(key,"key");
        String showType = bmConfigSvc.getConfigType(key);
        if (BinaryUtils.isEmpty(showType)) {
            showType = "1";
        }
        // key为assetManageShowType时表示是信息资产卡片的呈现形式 1: 分组，2:平铺
        return new RemoteResult(showType);
    }

    @GetMapping("updateAssetManageShowType")
    public RemoteResult updateAssetManageShowType(@RequestParam String key,@RequestParam String updateShowType) {
        verifyAuth.verifyAuth("品牌管理");
        BinaryUtils.checkEmpty(key,"key");
        DomainContextValue contextValue = DomainContextValue.builder().saas(saas).domainId(1L).domainReqType(DomainReqType.FRONT).build();
        DomainContext.setDomainContextValue(contextValue);
        Long id = bmConfigSvc.saveOrUpdateConfType(key, updateShowType,"");
        return new RemoteResult(id);
    }


    @GetMapping("getSystemColour")
    public RemoteResult getSystemColour(@RequestParam String key) {
        DomainContextValue contextValue = DomainContextValue.builder().saas(saas).domainId(1L).domainReqType(DomainReqType.FRONT).build();
        DomainContext.setDomainContextValue(contextValue);
        BinaryUtils.checkEmpty(key,"key");
        String showType = "light";
        // 为避免确实配置时出现异常导致后续接口，此处增加异常捕获
        try{
            showType = bmConfigSvc.getConfigType(key);
            if (BinaryUtils.isEmpty(showType)) {
                showType = "light";
            }
        }catch (Exception e){
            log.error("获取系统基础颜色错误",e);
        }
        return new RemoteResult(showType);
    }
}
